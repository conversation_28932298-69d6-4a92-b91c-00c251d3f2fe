# Docker 本地测试指南

## 🚀 快速开始

### 1. 准备环境
确保已安装 Docker 和 Docker Compose：
```bash
# 检查 Docker
docker --version
docker-compose --version

# 启动 Docker 服务（如果未启动）
# macOS: 启动 Docker Desktop
# Linux: sudo systemctl start docker
```

### 2. 给脚本添加执行权限
```bash
chmod +x test-local.sh
chmod +x deploy.sh
```

### 3. 本地测试
```bash
# 完整测试（构建 + 运行 + 交互）
./test-local.sh test

# 仅构建镜像
./test-local.sh build

# 清理所有资源
./test-local.sh cleanup
```

## 📋 测试步骤详解

### 步骤 1: 构建镜像
```bash
./test-local.sh build
```
这将：
- 检查 Docker 环境
- 清理旧的容器和镜像
- 使用 Dockerfile 构建新镜像

### 步骤 2: 运行测试
```bash
./test-local.sh test
```
这将：
- 执行步骤 1 的所有操作
- 启动容器
- 检查容器状态和健康检查
- 进入交互式测试模式

### 步骤 3: 交互式测试命令
在交互模式下，您可以使用：
- `logs` - 查看实时日志
- `exec` - 进入容器内部
- `stop` - 停止容器
- `restart` - 重启容器
- `quit` - 退出测试

## 🔍 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 查看详细构建日志
docker build --no-cache -t test-image .

# 检查 requirements.txt 是否有问题
cat requirements.txt
```

#### 2. 容器启动失败
```bash
# 查看容器日志
docker-compose -f docker-compose.local.yml logs ai-video-stats

# 检查容器状态
docker ps -a
```

#### 3. 数据库连接问题
```bash
# 进入容器检查网络
docker-compose -f docker-compose.local.yml exec ai-video-stats /bin/bash

# 在容器内测试数据库连接
python -c "from src.core.database import get_db; print('DB connection test')"
```

#### 4. 权限问题
```bash
# 检查文件权限
ls -la logs/ exports/

# 修复权限（如果需要）
sudo chown -R $USER:$USER logs/ exports/
```

## 📦 镜像导出和传输

### 导出镜像
```bash
# 构建并标记镜像
docker build -t ai-video-stats:latest .

# 导出镜像为 tar 文件
docker save -o ai-video-stats.tar ai-video-stats:latest

# 压缩镜像文件（可选）
gzip ai-video-stats.tar
```

### 传输到服务器
```bash
# 使用 scp 传输
scp ai-video-stats.tar.gz user@server:/path/to/destination/

# 或使用 rsync
rsync -avz ai-video-stats.tar.gz user@server:/path/to/destination/
```

### 在服务器上导入
```bash
# 解压（如果压缩了）
gunzip ai-video-stats.tar.gz

# 导入镜像
docker load -i ai-video-stats.tar

# 验证导入
docker images | grep ai-video-stats
```

## 🔧 配置说明

### 环境变量优先级
1. 环境变量（最高优先级）
2. .env 文件
3. conf/config.conf 文件（默认）

### 重要配置文件
- `Dockerfile` - 镜像构建配置
- `docker-compose.local.yml` - 本地测试配置
- `docker-compose.yml` - 生产环境配置
- `conf/config.conf` - 应用配置
- `.dockerignore` - 构建时忽略的文件

## ⚠️ 注意事项

1. **敏感信息**: 确保不要将包含敏感信息的配置文件提交到版本控制
2. **网络访问**: 容器需要能够访问外部数据库和飞书 API
3. **资源限制**: 根据服务器资源调整容器的内存和 CPU 限制
4. **日志管理**: 定期清理日志文件，避免磁盘空间不足
5. **备份**: 定期备份重要数据和配置

## 📝 下一步

测试成功后，您可以：
1. 使用 `docker save` 导出镜像
2. 传输到生产服务器
3. 使用 `deploy.sh` 脚本进行生产部署
4. 设置监控和日志收集
