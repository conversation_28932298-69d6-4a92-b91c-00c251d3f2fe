# AI视频制作统计服务

## 项目简介
本项目是一个AI视频制作统计服务，用于收集、处理和导出视频制作相关的统计数据。服务支持按日、周、月、季度等不同时间维度进行数据统计，并支持将数据自动同步到飞书表格及导出为Excel文件。

## 功能特点
- **自然时间统计**：支持自然日、自然周（周一至周日）、自然月（1号至月末）、自然季度（1-3月，4-6月，7-9月，10-12月）等时间维度的数据统计
- **飞书集成**：支持将统计数据自动同步到飞书表格
- **数据导出**：支持将统计数据导出为Excel文件
- **定时任务**：支持定时执行数据统计与同步任务
- **增量更新**：支持数据的增量更新，避免重复导出
- **多项目支持**：支持多个项目的数据统计与分析
- **服务控制**：提供完整的服务控制脚本，支持启动、停止、重启、状态查看等功能

## 统计指标
- AI任务统计：字幕移除、水印移除、换脸、视频分析、文本生成图片、图片生成视频、脚本创建等
- Agent任务统计：社交媒体Agent、广告Agent等
- 素材统计：素材数量、处理状态等
- 混剪项目统计：项目数量、视频数量等
- AI配音与翻译：AI配音任务、原文翻译任务等

## 环境要求
- Python 3.11+
- PostgreSQL 数据库
- 飞书开放平台应用配置（用于表格集成）
- 其他依赖见 requirements.txt

## 安装步骤
1. 克隆项目
```bash
git clone [项目地址]
cd ai_video_producer_stats
```

2. 创建并激活虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

## 使用说明

### 服务控制
使用控制脚本管理服务：
```bash
./control.sh start     # 启动服务
./control.sh stop      # 停止服务
./control.sh restart   # 重启服务
./control.sh status    # 查看服务状态
./control.sh run-once  # 运行一次数据更新
```

### 数据导出
运行数据导出脚本：
```bash
python export-data-to-excel.py
```

导出的数据将保存在 `exports` 目录下，文件名格式为：`stats_export_YYYYMMDD_HHMMSS.xlsx`

### 飞书集成
1. 在飞书开放平台创建应用并获取访问凭证
2. 服务会根据配置的时间间隔自动将数据同步到指定的飞书表格
3. 使用 `export-data-to-feishu.py` 脚本进行手动数据同步

### 时间统计维度
- **日统计**：每日零点至23:59:59
- **周统计**：自然周，从周一零点至周日23:59:59
- **月统计**：自然月，从每月1号零点至月末23:59:59
- **季度统计**：自然季度，按1-3月、4-6月、7-9月、10-12月计算

### 日志查看
日志文件保存在 `logs` 目录下，包含以下信息：
- 服务启动和停止信息
- 数据收集与同步过程
- 数据导出结果
- 错误信息（如果有）

## 目录结构
```
ai_video_producer_stats/
├── src/                 # 源代码目录
│   ├── core/            # 核心功能模块（数据库连接、配置等）
│   ├── models/          # 数据模型定义
│   ├── services/        # 业务服务实现
│   │   ├── stats_service.py    # 统计服务
│   │   └── feishu_service.py   # 飞书服务
│   ├── utils/           # 工具函数
│   ├── scripts/         # 脚本文件
│   └── tests/           # 单元测试
├── exports/             # 导出文件目录
├── logs/                # 日志目录
├── conf/                # 配置文件目录
├── ref/                 # 参考文档目录
├── app.py               # 主应用入口
├── export-data-to-excel.py  # Excel数据导出脚本
├── export-data-to-feishu.py # 飞书数据导出脚本
├── control.sh           # 服务控制脚本
├── requirements.txt     # 依赖包列表
├── setup.py             # 安装脚本
└── README.md            # 项目说明
```

## 开发说明
1. 保持日志记录的完整性
2. 提交前请确保本地测试通过

## 故障排除
1. 本服务只做数据库读取，禁止任何数据库写入操作
2. 飞书集成问题：确认飞书应用权限和访问凭证
3. 服务无法启动：查看 `logs` 目录下的最新日志
4. 数据统计不准确：检查时区配置是否为 'Asia/Shanghai'
5. 服务控制问题：确保 `control.sh` 具有执行权限（`chmod +x control.sh`）
