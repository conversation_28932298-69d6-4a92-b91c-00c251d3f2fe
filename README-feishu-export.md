# 数据导出到飞书互动表格

本工具用于将AI视频制作统计数据导出到飞书互动表格中，方便团队查看和分析。

## 功能特点

- 支持日、周、月、季度统计数据的收集和导出
- 支持按时间维度分别导出到不同的飞书互动表格
- 支持在导出前清空现有数据，确保数据准确无重复
- 批量处理数据，提高导出效率
- 智能过滤空数据，确保数据质量
- 支持将飞书数据备份到Excel
- 完善的日志记录和错误处理
- 支持命令行参数配置

## 环境变量配置

需要在项目根目录下创建`.env`文件，配置以下环境变量：

```
# 飞书API配置
FEISHU_APP_ID=cli_xxxxxxxxx           # 飞书应用的App ID
FEISHU_APP_SECRET=xxxxxxxxxxxxxx      # 飞书应用的App Secret

# 多表格配置（优先使用）
FEISHU_DAILY_TABLE_ID=tblxxxxxxxx     # 日数据表格ID
FEISHU_DAILY_VIEW_ID=vewxxxxxxxx      # 日数据视图ID
FEISHU_WEEKLY_TABLE_ID=tblxxxxxxxx    # 周数据表格ID
FEISHU_WEEKLY_VIEW_ID=vewxxxxxxxx     # 周数据视图ID
FEISHU_MONTHLY_TABLE_ID=tblxxxxxxxx   # 月数据表格ID
FEISHU_MONTHLY_VIEW_ID=vewxxxxxxxx    # 月数据视图ID
FEISHU_QUARTERLY_TABLE_ID=tblxxxxxxx  # 季度数据表格ID
FEISHU_QUARTERLY_VIEW_ID=vewxxxxxxx   # 季度数据视图ID

# 单表格配置（兼容旧版本）
FEISHU_APP_TOKEN=bascnxxxxxxx         # 多维表格的应用Token
FEISHU_TABLE_ID=tblxxxxxxxxxxxxxx     # 表格ID

# 可选配置
FEISHU_BATCH_SIZE=100                 # 批量处理记录数，默认100
BACKUP_TO_EXCEL=false                 # 是否同时备份到Excel，默认false
```

也可以在 `conf/config.conf` 中配置：

```ini
[feishu]
app_id = cli_xxxxxxxxx
app_secret = xxxxxxxxxxxxxx

# 日数据
daily_stats_table_id = tblxxxxxxxx
daily_stats_view_id = vewxxxxxxxx
# 周数据
weekly_stats_table_id = tblxxxxxxxx
weekly_stats_view_id = vewxxxxxxxx
# 月数据    
monthly_stats_table_id = tblxxxxxxxx
monthly_stats_view_id = vewxxxxxxxx
# 季度数据
quarterly_stats_table_id = tblxxxxxxx
quarterly_stats_view_id = vewxxxxxxxx
```

## 使用方法

### 命令行参数

```
  -h, --help            显示帮助信息
  --dimension {daily,weekly,monthly,quarterly}
                        指定导出的时间维度，不指定则导出所有维度
  --batch-size BATCH_SIZE
                        每批处理的记录数量，默认100
```

### 导出所有维度数据

```bash
python export-data-to-feishu.py
```

### 只导出日数据

```bash
python export-data-to-feishu.py --dimension daily
```

### 只导出周数据

```bash
python export-data-to-feishu.py --dimension weekly
```

### 数据清空与更新机制

当执行导出操作时，系统会按照以下步骤处理：

1. 收集指定维度（或所有维度）的统计数据
2. 在导入新数据前，自动清空飞书表格中对应维度的现有数据
3. 将新收集的数据批量导入到飞书表格中
4. 如果配置了备份选项，会同时将数据备份到Excel文件

这种机制确保了飞书表格中的数据始终是最新且无重复的。

### 配置定时任务

可以使用crontab设置定时任务，例如每天凌晨3点执行：

```bash
0 3 * * * cd /path/to/project && python export-data-to-feishu.py >> logs/cron.log 2>&1
```

## 飞书多维表格设置

在飞书多维表格中，每个时间维度的表格都需要创建以下字段：

1. 开始时间（文本字段）
2. 结束时间（文本字段）
3. 部门（文本字段）
4. 项目（文本字段）
5. 客户（文本字段）
6. AI字幕擦除任务数（数字字段）
7. AI水印擦除任务数（数字字段）
8. AI换脸任务数（数字字段）
9. AI换脸-视频数（数字字段）
10. AI视频解析任务数（数字字段）
11. AI文生图任务数（数字字段）
12. AI图生视频任务数（数字字段）
13. 创建脚本数（数字字段）
14. 素材数（数字字段）
15. 混剪任务数（数字字段）
16. 混剪任务-生成视频数（数字字段）
17. AI解说词配音-任务数（数字字段）
18. 原声翻译-任务数（数字字段）
19. 更新时间（文本字段）

## 故障排除

1. 如果遇到权限问题，请确保飞书应用具有读写表格的权限
2. 如果遇到API调用失败，可能是由于请求频率限制，可以调整`--batch-size`参数减小批处理大小
3. 清空数据操作失败可能是由于网络问题或API限制，系统会继续尝试导入新数据
4. 日志文件位于`logs/export_feishu.log`，可以查看详细的执行日志

## 开发者信息

本工具是AI视频制作统计系统的一部分，用于监控和分析AI视频制作的使用情况和效率。 