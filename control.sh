#!/bin/bash

# 服务控制脚本
APP_NAME="video_stats"
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$APP_DIR/logs/app.pid"
LOG_FILE="$APP_DIR/logs/app.log"
VENV_DIR="$APP_DIR/venv"

# 激活虚拟环境
source "$VENV_DIR/bin/activate"

# 检查PID文件是否存在
check_pid() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 启动服务
start() {
    if check_pid; then
        echo "Service is already running (PID: $PID)"
        return 1
    fi
    
    echo "Starting $APP_NAME..."
    nohup python "$APP_DIR/app.py" > "$LOG_FILE" 2>&1 &
    echo $! > "$PID_FILE"
    echo "Service started (PID: $(cat $PID_FILE))"
}

# 停止服务
stop() {
    if ! check_pid; then
        echo "Service is not running"
        return 1
    fi
    
    echo "Stopping $APP_NAME..."
    kill "$PID"
    rm -f "$PID_FILE"
    echo "Service stopped"
}

# 重启服务
restart() {
    stop
    sleep 2
    start
}

# 运行一次
run_once() {
    echo "Running $APP_NAME once (updating all reports)..."
    python "$APP_DIR/app.py" --run-once
    echo "One-time run completed"
}

# 查看服务状态
status() {
    if check_pid; then
        echo "Service is running (PID: $PID)"
    else
        echo "Service is not running"
    fi
}

# 命令行参数处理
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    run-once)
        run_once
        ;;
    scheduler)
        echo "Starting scheduler..."
        nohup python "$APP_DIR/scheduler.py" > "$APP_DIR/logs/scheduler.log" 2>&1 &
        echo $! > "$APP_DIR/logs/scheduler.pid"
        echo "Scheduler started"
        ;;
    stop-scheduler)
        if [ -f "$APP_DIR/logs/scheduler.pid" ]; then
            PID=$(cat "$APP_DIR/logs/scheduler.pid")
            if ps -p "$PID" > /dev/null 2>&1; then
                echo "Stopping scheduler..."
                kill "$PID"
                rm -f "$APP_DIR/logs/scheduler.pid"
                echo "Scheduler stopped"
            else
                echo "Scheduler is not running"
                rm -f "$APP_DIR/logs/scheduler.pid"
            fi
        else
            echo "Scheduler is not running"
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|run-once}"
        exit 1
        ;;
esac

exit 0
