import schedule
import time
import os
import sys
import subprocess
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/scheduler.log"),
        logging.StreamHandler()
    ]
)

def run_update_task():
    """运行更新任务"""
    try:
        logging.info("开始执行定时更新任务...")
        result = subprocess.run(["./control.sh", "run-once"], capture_output=True, text=True)
        if result.returncode == 0:
            logging.info("更新任务执行成功")
        else:
            logging.error(f"更新任务执行失败: {result.stderr}")
    except Exception as e:
        logging.error(f"更新任务执行异常: {str(e)}")

if __name__ == "__main__":
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    # 设置定时任务：每天9点和13点执行
    schedule.every().day.at("09:00").do(run_update_task)
    schedule.every().day.at("13:00").do(run_update_task)
    
    logging.info("定时任务调度器已启动")
    logging.info("已设置任务：每天9:00和13:00执行更新")
    
    # 无限循环，持续运行
    while True:
        schedule.run_pending()
        time.sleep(60)  # 每分钟检查一次