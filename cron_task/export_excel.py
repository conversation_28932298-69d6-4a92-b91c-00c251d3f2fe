import os
import sys
import psycopg2
from urllib.parse import quote_plus
from datetime import datetime, timedelta
import pytz
import pandas as pd
from conf import cnf

# 设置时区
tz = pytz.timezone("Asia/Shanghai")

# 创建到数据库的连接
def create_conn():
    conn = psycopg2.connect(
        host=cnf.database_master["host"],
        port=cnf.database_master["port"],
        database=cnf.database_master["database"],
        user=cnf.database_master["user"],
        password=quote_plus(cnf.database_master["password"])
    )
    return conn

def get_db_timezone():
    """获取数据库时区设置"""
    conn = create_conn()
    cur = conn.cursor()
    try:
        # 查询数据库当前时区设置
        cur.execute("SHOW TIMEZONE;")
        db_timezone = cur.fetchone()[0]
        
        # 查询数据库当前时间
        cur.execute("SELECT NOW();")
        db_time = cur.fetchone()[0]
        
        # 查询数据库时间和UTC时间的差值（小时）
        cur.execute("SELECT EXTRACT(TIMEZONE FROM NOW())/3600;")
        utc_offset = cur.fetchone()[0]
        
        # 也查询一些样本数据的时间，用于比较
        cur.execute("""
            SELECT 
                create_time,
                create_time AT TIME ZONE 'UTC',
                create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8',
                create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai',
                create_time AT TIME ZONE 'Asia/Shanghai'
            FROM base_task
            WHERE task_type='TASK_ERASE'
            LIMIT 5;
        """)
        sample_times = cur.fetchall()
        
        return {
            "db_timezone": db_timezone,
            "db_time": db_time,
            "utc_offset": utc_offset,
            "sample_times": sample_times
        }
    finally:
        cur.close()
        conn.close()

def get_erase_task_stats():
    """获取所有擦除任务的统计数据"""
    conn = create_conn()
    cur = conn.cursor()
    
    try:
        # 查询擦除任务统计数据，包含多种时区转换方式
        sql = """
        SELECT
            -- 按不同时区查询今日数据
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('day', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_day_utc_plus8,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'Asia/Shanghai' >= date_trunc('day', current_date AT TIME ZONE 'Asia/Shanghai')) AS erase_tasks_day_asia_shanghai,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')) AS erase_tasks_day_utc_minus8,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time >= date_trunc('day', current_date)) AS erase_tasks_day_default,
            
            -- 按不同时区查询本周数据
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_week_utc_plus8,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'Asia/Shanghai' >= date_trunc('week', current_date AT TIME ZONE 'Asia/Shanghai')) AS erase_tasks_week_asia_shanghai,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')) AS erase_tasks_week_utc_minus8,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time >= date_trunc('week', current_date)) AS erase_tasks_week_default,
            
            -- 按不同时区查询本月数据
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('month', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_month_utc_plus8,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'Asia/Shanghai' >= date_trunc('month', current_date AT TIME ZONE 'Asia/Shanghai')) AS erase_tasks_month_asia_shanghai,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')) AS erase_tasks_month_utc_minus8,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time >= date_trunc('month', current_date)) AS erase_tasks_month_default,
            
            -- 本季度和总计
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('quarter', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_quarter,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE') AS erase_tasks_all,
            
            -- 按task_type类型统计擦除任务明细
            (SELECT COUNT(*) FROM task_erase WHERE input_params->>'task_type' = 'TASK_ALL_ERASE') AS erase_tasks_all_type,
            (SELECT COUNT(*) FROM task_erase WHERE input_params->>'task_type' = 'TASK_WATERMARK_ERASE') AS erase_tasks_watermark_type,
            (SELECT COUNT(*) FROM task_erase WHERE input_params->>'task_type' = 'TASK_CAPTION_ERASE') AS erase_tasks_caption_type,
            
            -- 成功率
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND status = 'SUCCESS') AS erase_tasks_success,
            (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND status = 'FAIL') AS erase_tasks_fail,
            
            -- 当前时间
            NOW() AS current_time,
            NOW() AT TIME ZONE 'UTC' AS current_time_utc,
            NOW() AT TIME ZONE 'UTC+8' AS current_time_utc_plus8,
            NOW() AT TIME ZONE 'Asia/Shanghai' AS current_time_asia_shanghai
        """
        
        cur.execute(sql)
        return cur.fetchone()
    finally:
        cur.close()
        conn.close()

def main():
    # 获取数据库时区信息
    timezone_info = get_db_timezone()
    
    # 获取擦除任务统计
    erase_task_stats = get_erase_task_stats()
    
    # 创建数据帧
    timezone_df = pd.DataFrame({
        '配置项': ['数据库时区', '数据库当前时间', 'UTC偏移(小时)'],
        '值': [
            timezone_info['db_timezone'], 
            str(timezone_info['db_time']), 
            timezone_info['utc_offset']
        ]
    })
    
    # 创建样本时间数据帧
    sample_times_data = []
    for idx, sample in enumerate(timezone_info['sample_times']):
        sample_times_data.append({
            '记录ID': f'样本{idx+1}',
            '原始时间戳': sample[0],
            'UTC时间': sample[1],
            'UTC转UTC+8': sample[2],
            'UTC转Asia/Shanghai': sample[3],
            '直接转Asia/Shanghai': sample[4]
        })
    sample_times_df = pd.DataFrame(sample_times_data)
    
    # 创建擦除任务数据帧
    current_time = datetime.now(tz)
    stats_df = pd.DataFrame({
        '统计项': [
            f'今日擦除任务数(UTC+8)', 
            f'今日擦除任务数(Asia/Shanghai)', 
            f'今日擦除任务数(UTC-8)', 
            f'今日擦除任务数(默认时区)',
            f'本周擦除任务数(UTC+8)', 
            f'本周擦除任务数(Asia/Shanghai)', 
            f'本周擦除任务数(UTC-8)', 
            f'本周擦除任务数(默认时区)',
            f'本月擦除任务数(UTC+8)', 
            f'本月擦除任务数(Asia/Shanghai)', 
            f'本月擦除任务数(UTC-8)', 
            f'本月擦除任务数(默认时区)',
            f'本季度擦除任务数',
            f'所有擦除任务数',
            f'全部擦除类型任务数',
            f'水印擦除类型任务数',
            f'字幕擦除类型任务数',
            f'成功任务数',
            f'失败任务数',
            f'当前时间',
            f'当前UTC时间',
            f'当前UTC+8时间',
            f'当前Asia/Shanghai时间'
        ],
        '值': list(erase_task_stats)
    })
    
    # 创建与参考实现对比的数据帧
    comparison_df = pd.DataFrame({
        '数据来源': ['UTC+8(新)', 'Asia/Shanghai(新)', 'UTC-8(旧)', '默认时区'],
        '今日擦除任务数': [
            erase_task_stats[0], 
            erase_task_stats[1],
            erase_task_stats[2],
            erase_task_stats[3]
        ],
        '本周擦除任务数': [
            erase_task_stats[4], 
            erase_task_stats[5],
            erase_task_stats[6],
            erase_task_stats[7]
        ],
        '本月擦除任务数': [
            erase_task_stats[8], 
            erase_task_stats[9],
            erase_task_stats[10],
            erase_task_stats[11]
        ]
    })
    
    # 创建擦除任务类型统计数据帧
    erase_type_df = pd.DataFrame({
        '擦除任务类型': ['全部擦除(ALL_ERASE)', '水印擦除(WATERMARK_ERASE)', '字幕擦除(CAPTION_ERASE)', '总计(数据库中总任务数)'],
        '任务数量': [
            erase_task_stats[14],  # 全部擦除类型
            erase_task_stats[15],  # 水印擦除类型
            erase_task_stats[16],  # 字幕擦除类型
            erase_task_stats[13]   # 所有擦除任务数
        ],
        '任务比例': [
            f"{erase_task_stats[14]/erase_task_stats[13]*100:.2f}%" if erase_task_stats[13] > 0 else "0%", 
            f"{erase_task_stats[15]/erase_task_stats[13]*100:.2f}%" if erase_task_stats[13] > 0 else "0%",
            f"{erase_task_stats[16]/erase_task_stats[13]*100:.2f}%" if erase_task_stats[13] > 0 else "0%",
            "100%"
        ]
    })
    
    # 保存到Excel文件
    with pd.ExcelWriter('时区和擦除任务统计.xlsx') as writer:
        timezone_df.to_excel(writer, sheet_name='数据库时区信息', index=False)
        sample_times_df.to_excel(writer, sheet_name='样本时间比较', index=False)
        stats_df.to_excel(writer, sheet_name='擦除任务详细统计', index=False)
        comparison_df.to_excel(writer, sheet_name='不同时区对比', index=False)
        erase_type_df.to_excel(writer, sheet_name='擦除任务类型统计', index=False)
    
    print(f"统计数据已保存到 '时区和擦除任务统计.xlsx'")
    print(f"数据库时区: {timezone_info['db_timezone']}")
    print(f"UTC偏移: {timezone_info['utc_offset']}小时")
    print("\n不同时区对比:")
    print(comparison_df)

if __name__ == "__main__":
    main()