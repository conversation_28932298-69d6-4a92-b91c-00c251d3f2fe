import psycopg2
from contextlib import contextmanager
from datetime import datetime

@contextmanager
def create_conn():
    """创建数据库连接的上下文管理器"""
    conn = psycopg2.connect(
        host="vlc-hb-blueai-xy.pgdb.domob-inc.com",
        port=5432,
        database="ai_video_producer_pro",
        user="ai_video_producer_pro_ro",
        password="n7OvPC33o3nR",
    )
    try:
        yield conn
    finally:
        conn.close()

def query_face_swap_tasks():
    """查询2025年4月16日的换脸任务数量"""
    sql = """
        SELECT 
            COUNT(*) AS total_tasks,
            SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) AS successful_tasks
        FROM base_task
        WHERE 
            (task_type = 'TASK_FACE_SWAP' OR task_type = 'TASK_MULTI_FACE_SWAP')
            AND create_time AT TIME ZONE 'UTC' >= '2025-04-16 00:00:00'::timestamp
            AND create_time AT TIME ZONE 'UTC' < '2025-04-17 00:00:00'::timestamp;
    """
    with create_conn() as conn:
        try:
            with conn.cursor() as cur:
                cur.execute(sql)
                result = cur.fetchone()
                total_tasks, successful_tasks = result
                print(f"日期: 2025-04-16")
                print(f"换脸任务总数: {total_tasks}")
                print(f"成功的换脸任务数: {successful_tasks}")
        except Exception as e:
            print(f"查询失败: {e}")

def query_task_erase_types():
    """查询task_erase表中task_type的所有值"""
    sql = """
        SELECT DISTINCT input_params->>'task_type' AS task_type, COUNT(*) AS count
        FROM task_erase
        GROUP BY input_params->>'task_type'
        ORDER BY count DESC;
    """
    with create_conn() as conn:
        try:
            with conn.cursor() as cur:
                cur.execute(sql)
                results = cur.fetchall()
                
                print("task_erase表中的任务类型统计:")
                print("=" * 50)
                print("任务类型\t\t\t数量")
                print("-" * 50)
                
                for task_type, count in results:
                    if task_type:
                        print(f"{task_type}\t\t{count}")
                    else:
                        print(f"<空值>\t\t\t{count}")
                
                print("=" * 50)
                print(f"总计: {len(results)}种不同的任务类型")
                
                # 特别关注水印擦除和字幕擦除任务
                print("\n水印和字幕擦除任务详情:")
                print("-" * 50)
                
                # 查询带有WATERMARK的任务
                cur.execute("""
                    SELECT COUNT(*) FROM task_erase 
                    WHERE input_params->>'task_type' = 'TASK_WATERMARK_ERASE';
                """)
                watermark_count = cur.fetchone()[0]
                print(f"TASK_WATERMARK_ERASE (仅水印擦除): {watermark_count}个")
                
                # 查询带有CAPTION的任务
                cur.execute("""
                    SELECT COUNT(*) FROM task_erase 
                    WHERE input_params->>'task_type' = 'TASK_CAPTION_ERASE';
                """)
                caption_count = cur.fetchone()[0]
                print(f"TASK_CAPTION_ERASE (仅字幕擦除): {caption_count}个")
                
                # 查询ALL_ERASE任务
                cur.execute("""
                    SELECT COUNT(*) FROM task_erase 
                    WHERE input_params->>'task_type' = 'TASK_ALL_ERASE';
                """)
                all_erase_count = cur.fetchone()[0]
                print(f"TASK_ALL_ERASE (全部擦除): {all_erase_count}个")
                
        except Exception as e:
            print(f"查询失败: {e}")

def check_database_timezone():
    """检查数据库中create_time字段的时区信息"""
    sql_list = [
        # 检查数据库时区设置
        "SHOW timezone;",
        
        # 获取当前数据库时间和UTC时间的对比
        """
        SELECT 
            NOW() AS db_current_time,
            NOW() AT TIME ZONE 'UTC' AS utc_time,
            NOW() AT TIME ZONE 'Asia/Shanghai' AS china_time;
        """,
        
        # 检查一条最新记录的时间信息
        """
        SELECT 
            create_time AS original_time,
            create_time AT TIME ZONE 'UTC' AS utc_time,
            create_time AT TIME ZONE 'Asia/Shanghai' AS china_time,
            pg_typeof(create_time) AS data_type
        FROM base_task 
        ORDER BY create_time DESC 
        LIMIT 1;
        """,
        
        # 检查create_time字段的类型定义
        """
        SELECT 
            column_name, 
            data_type, 
            datetime_precision
        FROM information_schema.columns
        WHERE table_name = 'base_task' AND column_name = 'create_time';
        """,
        
        # 检查PostgreSQL的时区设置
        "SELECT name, setting FROM pg_settings WHERE name LIKE '%time_zone%';"
    ]
    
    with create_conn() as conn:
        try:
            with conn.cursor() as cur:
                # 查询数据库时区设置
                print("数据库时区信息:")
                print("=" * 50)
                
                cur.execute(sql_list[0])
                db_timezone = cur.fetchone()[0]
                print(f"数据库时区设置: {db_timezone}")
                
                # 查询当前时间对比
                cur.execute(sql_list[1])
                time_result = cur.fetchone()
                print(f"\n当前数据库时间: {time_result[0]}")
                print(f"UTC时间: {time_result[1]}")
                print(f"中国时间: {time_result[2]}")
                
                # 查询一条记录的时间信息
                print("\n最新记录的时间信息:")
                print("-" * 50)
                cur.execute(sql_list[2])
                record_time = cur.fetchone()
                print(f"原始存储时间: {record_time[0]}")
                print(f"转换为UTC时间: {record_time[1]}")
                print(f"转换为中国时间: {record_time[2]}")
                print(f"时间字段数据类型: {record_time[3]}")
                
                # 查询字段类型定义
                print("\ncreate_time字段定义:")
                print("-" * 50)
                cur.execute(sql_list[3])
                column_info = cur.fetchone()
                if column_info:
                    print(f"列名: {column_info[0]}")
                    print(f"数据类型: {column_info[1]}")
                    print(f"时间精度: {column_info[2]}")
                else:
                    print("未找到create_time字段信息")
                
                # 查询PostgreSQL时区设置
                print("\nPostgreSQL时区相关设置:")
                print("-" * 50)
                cur.execute(sql_list[4])
                timezone_settings = cur.fetchall()
                for name, setting in timezone_settings:
                    print(f"{name}: {setting}")
                
                print("\n结论:")
                print("-" * 50)
                if db_timezone.upper() == 'UTC':
                    print("数据库使用UTC时区存储时间。使用时需要先转换到本地时区。")
                    print("推荐时区处理方式: create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai'")
                else:
                    print(f"数据库使用{db_timezone}时区存储时间。请注意时区转换。")
                    
        except Exception as e:
            print(f"查询失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("\n===== 查询擦除任务类型 =====")
    query_task_erase_types()
    
    print("\n\n===== 检查数据库时区信息 =====")
    check_database_timezone()
    
    # 如需查询换脸任务，可以取消下面的注释
    # print("\n\n===== 查询换脸任务 =====")
    # query_face_swap_tasks()