import argparse
import os
import sys

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
import psycopg2
from urllib.parse import quote_plus
import requests
import json
from datetime import datetime, timedelta
import pytz
from conf import cnf

tz = pytz.timezone("Asia/Shanghai")

DEFAULT_ROBOT_URL_LIST = [
    # "https://open.feishu.cn/open-apis/bot/v2/hook/f331330e-fc2b-4ba9-990b-796e6a5770c5",  # 品鉴
    "https://open.feishu.cn/open-apis/bot/v2/hook/fff3c5f7-539c-429a-8a3e-0a9d7ec340e9",  # 心影
    "https://open.feishu.cn/open-apis/bot/v2/hook/3ff20243-493e-4f19-a5ea-9d0c447c5c1c"
]


# 创建到数据库的连接
def create_conn():
    conn = psycopg2.connect(
        host=cnf.database_master["host"],
        port=cnf.database_master["port"],
        database=cnf.database_master["name"],
        user=cnf.database_master["user"],
        password=quote_plus(cnf.database_master["pawd"]),
    )
    return conn


sql = f"""SELECT
    -- TASK_ERASE counts
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('day', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_day,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND status = 'SUCCESS' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('day', current_date AT TIME ZONE 'UTC+8')) AS success_erase_tasks_day,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_week,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND status = 'SUCCESS' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')) AS success_erase_tasks_week,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('month', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_month,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND status = 'SUCCESS' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('month', current_date AT TIME ZONE 'UTC+8')) AS success_erase_tasks_month,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('quarter', current_date AT TIME ZONE 'UTC+8')) AS erase_tasks_quarter,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND status = 'SUCCESS' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('quarter', current_date AT TIME ZONE 'UTC+8')) AS success_erase_tasks_quarter,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE') AS erase_tasks_all,
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_ERASE' AND status = 'SUCCESS') AS success_erase_tasks_all,


    -- TASK_FACE_SWAP counts
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND create_time >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' AND create_time >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_FACE_SWAP' OR task_type='TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS'),
    
    -- TASK_STORY_BOARD_ANA counts
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND create_time  >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND status = 'SUCCESS' AND create_time  >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND status = 'SUCCESS' AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND status = 'SUCCESS' AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND status = 'SUCCESS' AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA'),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_STORY_BOARD_ANA' AND status = 'SUCCESS'),

    -- TASK_MJ_TXT2IMG counts
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND create_time  >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND status = 'SUCCESS' AND create_time  >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND status = 'SUCCESS' AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND status = 'SUCCESS' AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND status = 'SUCCESS' AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG'),
    (SELECT COUNT(*) FROM base_task WHERE task_type='TASK_MJ_TXT2IMG' AND status = 'SUCCESS'),
    
    -- TASK_IMG2VIDEO counts
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND create_time  >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND status = 'SUCCESS' AND create_time  >= date_trunc('day', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND status = 'SUCCESS' AND create_time  >= date_trunc('week', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND status = 'SUCCESS' AND create_time  >= date_trunc('month', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND status = 'SUCCESS' AND create_time  >= date_trunc('quarter', current_date AT TIME ZONE 'UTC-8')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX')),
    (SELECT COUNT(*) FROM base_task WHERE (task_type='TASK_IMG2VIDEO' OR task_type='TASK_IMG2VIDEO_MINIMAX') AND status = 'SUCCESS')
"""

# 创建一个游标对象
def get_data_from_db():
    conn = create_conn()
    cur = conn.cursor()
    try:
        # 执行SQL查询
        cur.execute(sql)
        # 获取查询结果
        rows = cur.fetchall()
        # 打印查询结果
        res = list(rows)
        for row in res:
            print(row)
        # 关闭游标和连接
        return res
    finally:
        cur.close()
        conn.close()


def create_feishu_message(data):


    # 在实践周期上注明统计的年月日
    time_ranges = ["今日", "本周", "本月", "本季度", "上线以来"]

    print(f"原始查询结果>>> {data}")

    # 对data数据按照时间区间进行分组
    # 输入：[(15, 15, 11, 11, 6, 6, 9, 8, 7, 7, 15, 15, 11, 11, 6, 6, 9, 8, 7, 7, 22, 22, 31, 15, 97, 90, 303, 233, 76, 72, 22, 22, 31, 15, 97, 90, 303, 233, 76, 72, 269, 264, 171, 116, 903, 868, 500, 418, 177, 158)]
    # 输出：[[(15, 15), (11, 11), (6, 6), (9, 8), (7, 7)], [(15, 15), (11, 11), (6, 6), (9, 8), (7, 7)], [(22, 22), (31, 15), (97, 90), (303, 233), (76, 72)], [(22, 22), (31, 15), (97, 90), (303, 233), (76, 72)], [(269, 264), (171,
    # 116), (903, 868), (500, 418), (177, 158)]]

    # 将数据拆分为每两个元素一组
    grouped_data = [tuple(data[0][i:i + 2]) for i in range(0, len(data[0]), 2)]

    # 将每一组数据拆分为子列表，假设每个子列表包含5个元组
    rows = [grouped_data[i:i + 5] for i in range(0, len(grouped_data), 5)]


    print(f"数据分组>>> {rows}")
    table_lists = ["task_erase", "change_face", "video_analysis", "text2img", "img2video"]
    template_variable = {}
    # 对每个table区间的数据进行处理
    for i, row in enumerate(rows):
        table_data = []
        for j, daily_data in enumerate(row):
            time_range = time_ranges[j]
            all_task_num = daily_data[0]
            task_success_num = daily_data[1]
            task_fail_num = daily_data[0]-daily_data[1]
            if all_task_num > 0:
                task_success_rate = round(task_success_num / all_task_num * 100, 2)
            else:
                task_success_rate = 0
            table_data.append(
                {
                    "time_range": time_ranges[j],
                    "all_task_num": all_task_num,
                    "task_success_num": task_success_num,
                    "task_fail_num": task_fail_num,
                    "task_success_rate": f"{task_success_rate}%",
                }
            )
        template_variable[f"{table_lists[i]}"] = table_data
    if cnf.runtime["mode"] == "develop":
        template_variable["env"] = "线下"
    else:
        template_variable["env"] = "线上"

    return {
        "msg_type": "interactive",
        "card": {
            "type": "template",
            "data": {
                "template_id": "AAqD47mFsP0nx",
                "template_version_name": "1.0.2",
                "template_variable": template_variable,
            },
        },
    }


def send2feishu(hooks, message):
    for url in hooks:
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, headers=headers, json=message)
        if response.status_code == 200:
            print("Message sent successfully")
        else:
            print(f"Failed to send message: {response.status_code}, {response.text}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Send Feishu message with task data.")
    parser.add_argument(
        "-robot_url",
        type=str,
        nargs="*",
        default=DEFAULT_ROBOT_URL_LIST,
        help="List of Feishu robot URLs to send the message to. (e.g. --robot_url_list <url1> <url2>)"
    )
    args = parser.parse_args()
    res = get_data_from_db()
    message = create_feishu_message(res)
    send2feishu(args.robot_url, message)
