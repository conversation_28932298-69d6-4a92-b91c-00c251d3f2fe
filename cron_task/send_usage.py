import argparse
import os
import sys

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
import psycopg2
from urllib.parse import quote_plus
import requests
import json
from datetime import datetime, timedelta
import pytz
from conf import cnf

tz = pytz.timezone("Asia/Shanghai")

DEFAULT_ROBOT_URL_LIST = [
    # "https://open.feishu.cn/open-apis/bot/v2/hook/f331330e-fc2b-4ba9-990b-796e6a5770c5",  # 品鉴
    "https://open.feishu.cn/open-apis/bot/v2/hook/fff3c5f7-539c-429a-8a3e-0a9d7ec340e9",  # 心影
    "https://open.feishu.cn/open-apis/bot/v2/hook/3ff20243-493e-4f19-a5ea-9d0c447c5c1c"
]


# 创建到数据库的连接
def create_conn():
    conn = psycopg2.connect(
        host=cnf.database_master["host"],
        port=cnf.database_master["port"],
        database=cnf.database_master["name"],
        user=cnf.database_master["user"],
        password=quote_plus(cnf.database_master["pawd"]),
    )
    return conn


sql = f"""SELECT
    -- Weekly counts
    (SELECT COUNT(*) FROM story_board WHERE created_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC' >= date_trunc('week', current_date AT TIME ZONE 'UTC')) AS new_scripts_week,
    (SELECT COUNT(*) FROM material WHERE created_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC' >= date_trunc('week', current_date AT TIME ZONE 'UTC')) AS new_materials_week,
    (SELECT COUNT(*) FROM task_mix_artwork WHERE create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')) AS new_mix_tasks_week,
    (SELECT COUNT(*) FROM mix_artwork JOIN task_mix_artwork ON mix_artwork.source_task_id = task_mix_artwork.sub_task_id WHERE task_mix_artwork.create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')) AS new_generated_videos_week,

    -- Monthly counts
    (SELECT COUNT(*) FROM story_board WHERE created_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC' >= date_trunc('month', current_date AT TIME ZONE 'UTC')) AS new_scripts_month,
    (SELECT COUNT(*) FROM material WHERE created_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC' >= date_trunc('month', current_date AT TIME ZONE 'UTC')) AS new_materials_month,
    (SELECT COUNT(*) FROM task_mix_artwork WHERE create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('month', current_date AT TIME ZONE 'UTC+8')) AS new_mix_tasks_month,
    (SELECT COUNT(*) FROM mix_artwork JOIN task_mix_artwork ON mix_artwork.source_task_id = task_mix_artwork.sub_task_id WHERE task_mix_artwork.create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('month', current_date AT TIME ZONE 'UTC+8')) AS new_generated_videos_month,

    -- Quarterly counts
    (SELECT COUNT(*) FROM story_board WHERE created_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC' >= date_trunc('quarter', current_date AT TIME ZONE 'UTC')) AS new_scripts_quarter,
    (SELECT COUNT(*) FROM material WHERE created_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC' >= date_trunc('quarter', current_date AT TIME ZONE 'UTC')) AS new_materials_quarter,
    (SELECT COUNT(*) FROM task_mix_artwork WHERE create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('quarter', current_date AT TIME ZONE 'UTC+8')) AS new_mix_tasks_quarter,
    (SELECT COUNT(*) FROM mix_artwork JOIN task_mix_artwork ON mix_artwork.source_task_id = task_mix_artwork.sub_task_id WHERE task_mix_artwork.create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('quarter', current_date AT TIME ZONE 'UTC+8')) AS new_generated_videos_quarter,

    -- Total counts since system went live
    (SELECT COUNT(*) FROM story_board) AS total_new_scripts,
    (SELECT COUNT(*) FROM material) AS total_new_materials,
    (SELECT COUNT(*) FROM task_mix_artwork) AS total_new_mix_tasks,
    (SELECT COUNT(*) FROM mix_artwork JOIN task_mix_artwork ON mix_artwork.source_task_id = task_mix_artwork.sub_task_id) AS total_new_generated_videos;
"""


# 创建一个游标对象
def get_data_from_db():
    conn = create_conn()
    cur = conn.cursor()
    try:
        # 执行SQL查询
        cur.execute(sql)
        # 获取查询结果
        rows = cur.fetchall()
        # 打印查询结果
        res = list(rows)
        for row in res:
            print(row)
        # 关闭游标和连接
        return res
    finally:
        cur.close()
        conn.close()


def create_feishu_message(data):
    table_data = []

    # 在实践周期上注明统计的年月日
    time_ranges = ["本周", "本月", "本季度", "上线以来"]

    print(f"原始查询结果>>> {data}")

    # 对data数据按照时间区间进行分组
    # 输入：[(63, 249, 72, 60, 90, 1453, 354, 113, 90, 1453, 354, 113, 1771, 6415, 2808, 882)]
    # 输出：[[63, 249, 72, 60], [90, 1453, 354, 113], [90, 1453, 354, 113], [1771, 6415, 2808, 882]]
    rows = []

    for i in range(0, len(data[0]), 4):
        rows.append(data[0][i : i + 4])

    print(f"数据分组>>> {rows}")

    # 对每个时间区间的数据进行处理
    for index, row in enumerate(rows):
        storyboard_cnt = row[0]
        material_cnt = row[1]
        mix_project_cnt = row[2]
        mix_artwork_cnt = row[3]

        table_data.append(
            {
                "time_range": time_ranges[index],
                "storyboard_cnt": storyboard_cnt,
                "material_cnt": material_cnt,
                "mix_project_cnt": mix_project_cnt,
                "mix_artwork_cnt": mix_artwork_cnt,
            }
        )

    return {
        "msg_type": "interactive",
        "card": {
            "type": "template",
            "data": {
                "template_id": "AAqDVyjrKE7SZ",
                "template_version_name": "1.0.4",
                "template_variable": {
                    "env2": "线下" if cnf.runtime["mode"] == "develop" else "线上",
                    "table_ai_video_producer_stats": table_data,
                },
            },
        },
    }


def send2feishu(hooks, message):
    for url in hooks:
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, headers=headers, json=message)
        if response.status_code == 200:
            print("Message sent successfully")
        else:
            print(f"Failed to send message: {response.status_code}, {response.text}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Send Feishu message with task data.")
    parser.add_argument(
        "-robot_url",
        type=str,
        nargs="*",
        default=DEFAULT_ROBOT_URL_LIST,
        help="List of Feishu robot URLs to send the message to. (e.g. --robot_url_list <url1> <url2>)"
    )
    args = parser.parse_args()
    res = get_data_from_db()
    message = create_feishu_message(res)
    print(message)
    send2feishu(args.robot_url, message)
