import argparse
import os
import sys

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
import psycopg2
from urllib.parse import quote_plus
import requests
import json
from datetime import datetime, timedelta
import pytz
from conf import cnf

tz = pytz.timezone("Asia/Shanghai")

DEFAULT_ROBOT_URL_LIST = [
    # "https://open.feishu.cn/open-apis/bot/v2/hook/f331330e-fc2b-4ba9-990b-796e6a5770c5",  # 品鉴
    "https://open.feishu.cn/open-apis/bot/v2/hook/fff3c5f7-539c-429a-8a3e-0a9d7ec340e9",  # 心影
    "https://open.feishu.cn/open-apis/bot/v2/hook/3ff20243-493e-4f19-a5ea-9d0c447c5c1c"
]


# 创建到数据库的连接
def create_conn():
    conn = psycopg2.connect(
        host=cnf.database_master["host"],
        port=cnf.database_master["port"],
        database=cnf.database_master["name"],
        user=cnf.database_master["user"],
        password=quote_plus(cnf.database_master["pawd"]),
    )
    return conn


# task_types = ["TASK_ERASE", "TASK_FACE_SWAP", "TASK_STORY_BOARD_ANA", "TASK_MJ_TXT2IMG", "TASK_IMG2VIDEO"]

sql_lists = [
    ["SELECT COUNT(*) FROM task_mj_txt2img WHERE result::text NOT LIKE '%生成失败的任务不消耗任务额度%' AND create_time  >= date_trunc('day', current_date)",
     "SELECT input_params, model FROM task_img2video WHERE create_time  >= date_trunc('day', current_date)"],

    ["SELECT COUNT(*) FROM task_mj_txt2img WHERE result::text NOT LIKE '%生成失败的任务不消耗任务额度%' AND create_time  >= date_trunc('week', current_date)",
     "SELECT input_params, model FROM task_img2video WHERE create_time  >= date_trunc('week', current_date)"],

    ["SELECT COUNT(*) FROM task_mj_txt2img WHERE result::text NOT LIKE '%生成失败的任务不消耗任务额度%' AND create_time  >= date_trunc('month', current_date)",
     "SELECT input_params, model FROM task_img2video WHERE create_time  >= date_trunc('month', current_date)"],

    ["SELECT COUNT(*) FROM task_mj_txt2img WHERE result::text NOT LIKE '%生成失败的任务不消耗任务额度%' AND create_time  >= date_trunc('quarter', current_date)",
     "SELECT input_params, model FROM task_img2video WHERE create_time  >= date_trunc('quarter', current_date)"],

    ["SELECT COUNT(*) FROM task_mj_txt2img WHERE result::text NOT LIKE '%生成失败的任务不消耗任务额度%'",
     "SELECT input_params, model FROM task_img2video"]
]




def get_data_from_db(sql):
    conn = create_conn()
    cur = conn.cursor()
    try:
        # 执行SQL查询
        cur.execute(sql)
        # 获取查询结果
        rows = cur.fetchall()
        # 打印查询结果
        res = list(rows)
        for row in res:
            print(row)
        # 关闭游标和连接
        return res
    except Exception as e:
        print(e)
    finally:
        cur.close()
        conn.close()


def create_feishu_message():
    table_data = []
    time_ranges = ["今日", "本周", "本月", "本季度", "上线以来"]
    # time_ranges = ["本周", "本月", "本季度", "上线以来"]
    # 总计消耗点数
    total_points = 0
    for index, sql in enumerate(sql_lists):
        t2i_count = get_data_from_db(sql[0])
        i2v_data = get_data_from_db(sql[1])
        # 点数计算
        points = 0
        i2v_minimax_count = 0
        for val in i2v_data:
            if val[1] == "video-01":
                i2v_minimax_count += 1

            elif val[1] == "kling-v1":
                if val[0].get("mode") is not None:
                    if val[0]["mode"] == "pro":
                        points += 3.5
                    elif val[0]["mode"] == "std":
                        points += 1

        if index == 2:
            total_points = points

        time_range = time_ranges[index]
        text2img = t2i_count[0][0]
        img2video = points

        table_data.append(
            {
                "time_range": time_range,
                "text2img": text2img,
                "img2video_kling": img2video,
                "img2video_minimax": i2v_minimax_count
            }
        )
    if cnf.runtime["mode"] == "develop":
        env = "线下"
    else:
        env = "线上"
    return {
        "msg_type": "interactive",
        "card": {
            "type": "template",
            "data": {
                "template_id": "AAqD4CJWCoNww",
                "template_version_name": "1.0.4",
                "template_variable": {
                    "env": env,
                    "remain_points": 30000 - total_points,
                    "usage_statistics": table_data,
                },
            },
        },
    }

def send2feishu(hooks, message):
    for url in hooks:
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, headers=headers, json=message)
        if response.status_code == 200:
            print("Message sent successfully")
        else:
            print(f"Failed to send message: {response.status_code}, {response.text}")



if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Send Feishu message with task data.")
    parser.add_argument(
        "-robot_url",
        type=str,
        nargs="*",
        default=DEFAULT_ROBOT_URL_LIST,
        help="List of Feishu robot URLs to send the message to. (e.g. --robot_url_list <url1> <url2>)"
    )
    args = parser.parse_args()
    message = create_feishu_message()
    send2feishu(args.robot_url, message)