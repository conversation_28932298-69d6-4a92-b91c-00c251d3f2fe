import argparse
import os
import sys

import requests

sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
import psycopg2
from urllib.parse import quote_plus
import pytz
import ast
from conf import cnf
from src.utils.rpc_retriever import llm_call

tz = pytz.timezone("Asia/Shanghai")

DEFAULT_ROBOT_URL_LIST = [
    # "https://open.feishu.cn/open-apis/bot/v2/hook/f331330e-fc2b-4ba9-990b-796e6a5770c5",  # 品鉴
    "https://open.feishu.cn/open-apis/bot/v2/hook/fff3c5f7-539c-429a-8a3e-0a9d7ec340e9",  # 心影
    "https://open.feishu.cn/open-apis/bot/v2/hook/3ff20243-493e-4f19-a5ea-9d0c447c5c1c",
]


# 创建到数据库的连接
def create_conn():
    conn = psycopg2.connect(
        host=cnf.database_master["host"],
        port=cnf.database_master["port"],
        database=cnf.database_master["name"],
        user=cnf.database_master["user"],
        password=quote_plus(cnf.database_master["pawd"]),
    )
    return conn

sql_lists = [
    "SELECT sub_task_id,result FROM task_erase WHERE result::text LIKE '%Error%' AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')",
    "select sub_task_id,err_msg from task_face_swap where err_msg is not NULL AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')",
    "select sub_task_id,err_msg from task_story_board_ana where err_msg is not NULL AND end_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')",
    "select sub_task_id,err_msg from task_mj_txt2img where err_msg is not NULL AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')",
    "select sub_task_id,err_msg from task_img2video where err_msg is not NULL AND create_time AT TIME ZONE 'UTC' AT TIME ZONE 'UTC+8' >= date_trunc('week', current_date AT TIME ZONE 'UTC+8')",
]

def gpt4o_analysis(data):
    prompt = f"""我将给你数据库查询内容，请你用中文概述错误信息并返回结果，
                        注意下面几点：
                        1、err_msg用一句中文简单概述错误类型；
                        2、我只需要结果即可，不需要解析描述；
                        3、只按照输出格式输出，不要加类似结果为这样的语句；
                        4、查询内容为空的情况也需要返回id。
                        ## input format
                        {data}

                        ## output format
                        'sub_task_id': 'xxxx', 'err_msg': 'xxxx',
                        'sub_task_id': 'xxxx', 'err_msg': 'xxxx'
                        """
    result = llm_call(prompt=prompt, engine="gpt4o", caller="failed_msg_analysis")
    return result


def get_data_from_db(sql):
    conn = create_conn()
    cur = conn.cursor()
    try:
        # 执行SQL查询
        cur.execute(sql)
        # 获取查询结果
        rows = cur.fetchall()
        # 打印查询结果
        res = list(rows)
        for row in res:
            print(row)
        # 关闭游标和连接
        return res
    finally:
        cur.close()
        conn.close()

def create_feishu_message(sql_lists):
    # time_ranges = ["本日", "本周", "本月", "本季度", "上线以来"]
    task_type_list = ["AI擦除", "AI换脸", "AI视频解析", "文生图", "图生视频"]
    # 错误字典
    msg_data = {}
    table_data = []
    for index, sql in enumerate(sql_lists):
        msg_data[task_type_list[index]] = {}
        data = get_data_from_db(sql)
        for item in data:
            # gpt4o分析
            result = gpt4o_analysis(item)
            # 修改为有效的字典格式
            str_data = "{ " + result + " }"  # 添加大括号使其成为字典

            # 使用 literal_eval 转换为字典
            result = ast.literal_eval(str_data)
            if result["err_msg"] not in msg_data[task_type_list[index]]:
                msg_data[task_type_list[index]][result["err_msg"]] = [0]
                msg_data[task_type_list[index]][result["err_msg"]][0] += 1
                msg_data[task_type_list[index]][result["err_msg"]].append(result["sub_task_id"])
            else:
                msg_data[task_type_list[index]][result["err_msg"]][0] += 1
                msg_data[task_type_list[index]][result["err_msg"]].append(result["sub_task_id"])

        for key, val in msg_data[task_type_list[index]].items():
            table_data.append(
                {
                    "task_type":task_type_list[index],
                    "fail_type": key,
                    "fail_num": val[0],
                    "sub_task_id": f"{val[1:]}",
                }
            )
        print(table_data)
    if cnf.runtime["mode"] == "develop":
        env = "线下"
    else:
        env = "线上"

    return {
        "msg_type": "interactive",
        "card": {
            "type": "template",
            "data": {
                "template_id": "AAqDDnkv7eap6",
                "template_version_name": "1.0.3",
                "template_variable": {
                    "env": env,
                    "task_failed": table_data,
                },
            },
        },
    }

def send2feishu(hooks, message):
    for url in hooks:
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, headers=headers, json=message)
        if response.status_code == 200:
            print("Message sent successfully")
        else:
            print(f"Failed to send message: {response.status_code}, {response.text}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Send Feishu message with task data.")
    parser.add_argument(
        "-robot_url",
        type=str,
        nargs="*",
        default=DEFAULT_ROBOT_URL_LIST,
        help="List of Feishu robot URLs to send the message to. (e.g. --robot_url_list <url1> <url2>)"
    )
    args = parser.parse_args()
    message = create_feishu_message(sql_lists)
    send2feishu(args.robot_url, message)