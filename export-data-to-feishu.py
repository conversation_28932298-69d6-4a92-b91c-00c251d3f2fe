import os
import sys
from datetime import datetime, timedelta
from loguru import logger
from tqdm import tqdm
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scripts.export_stats import StatsExporter
from src.services.stats_service import StatsService
from src.services.feishu_service import FeishuService

def verify_erase_task_count():
    """验证AI擦除任务的计数是否正确"""
    try:
        # 创建统计服务实例
        stats_service = StatsService()
        
        # 收集历史日统计数据
        logger.info("收集历史日统计数据...")
        daily_stats = stats_service.collect_historical_daily_stats()
        
        # 分析擦除任务统计数据
        total_erase_tasks = 0
        
        for stat in daily_stats:
            total_erase_tasks += stat.erase_tasks
        
        logger.info("AI擦除任务统计验证结果:")
        logger.info(f"总记录数: {len(daily_stats)}")
        logger.info(f"AI擦除任务总数: {total_erase_tasks}")
        
        # 检查是否存在异常大的数值
        if total_erase_tasks > 10000:
            logger.warning(f"⚠️ AI擦除任务总数异常大: {total_erase_tasks}，可能存在重复计算")
        elif total_erase_tasks < 100 and len(daily_stats) > 0:
            logger.warning(f"⚠️ AI擦除任务总数异常小: {total_erase_tasks}，可能存在计算问题")
        else:
            logger.info("✅ AI擦除任务总数在合理范围内")
            
        return True
    except Exception as e:
        logger.error(f"验证过程中发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def export_stats_to_feishu(batch_size=500, dimension=None):
    """将统计数据导出到飞书互动表格
    
    Args:
        batch_size: 每批处理的记录数量，默认100
        dimension: 指定导出的时间维度，为None时导出所有维度
        
    Returns:
        bool: 导出是否成功
    """
    try:
        # 创建统计服务实例
        stats_service = StatsService()
        
        # 创建飞书服务实例
        feishu_service = FeishuService()
        
        all_stats = []
        
        # 根据指定的维度收集数据
        if not dimension or dimension == 'daily':
            # 收集历史日统计数据
            logger.info("收集历史日统计数据...")
            daily_stats = stats_service.collect_historical_daily_stats()
            logger.info(f"收集到 {len(daily_stats)} 条日统计数据")
            all_stats.extend(daily_stats)
        
        if not dimension or dimension == 'weekly':
            # 收集历史周统计数据
            logger.info("收集历史周统计数据...")
            weekly_stats = stats_service.collect_historical_weekly_stats()
            logger.info(f"收集到 {len(weekly_stats)} 条周统计数据")
            all_stats.extend(weekly_stats)
        
        if not dimension or dimension == 'monthly':
            # 收集历史月统计数据
            logger.info("收集历史月统计数据...")
            monthly_stats = stats_service.collect_historical_monthly_stats()
            logger.info(f"收集到 {len(monthly_stats)} 条月统计数据")
            all_stats.extend(monthly_stats)
        
        if not dimension or dimension == 'quarterly':
            # 收集历史季度统计数据
            logger.info("收集历史季度统计数据...")
            quarterly_stats = stats_service.collect_historical_quarterly_stats()
            logger.info(f"收集到 {len(quarterly_stats)} 条季度统计数据")
            all_stats.extend(quarterly_stats)
        
        logger.info(f"总计收集到 {len(all_stats)} 条统计数据")
        
        # 先清空飞书表格中的数据
        logger.info("开始清空飞书表格中的现有数据...")
        clear_success = feishu_service.clear_all_tables_data(
            batch_size=batch_size,
            show_progress=True,
            dimension=dimension
        )
        
        if not clear_success:
            logger.warning("清空飞书表格数据失败或部分失败，继续尝试导出数据...")
        
        # 导出数据到飞书
        logger.info("开始导出数据到飞书互动表格...")
        success = feishu_service.update_table(
            records=all_stats,
            batch_size=batch_size,
            show_progress=True
        )
        
        # 根据需要，同时导出到Excel进行备份
        if success and os.getenv("BACKUP_TO_EXCEL", "false").lower() == "true":
            try:
                export_dir = "exports"
                if not os.path.exists(export_dir):
                    os.makedirs(export_dir)
                    
                backup_file = os.path.join(
                    export_dir, 
                    f"feishu_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                )
                feishu_service.export_to_excel(backup_file, dimension=dimension)
                logger.info(f"已将飞书数据备份到Excel: {backup_file}")
            except Exception as e:
                logger.error(f"备份到Excel失败: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                # 备份失败不影响主流程
            
        return success
        
    except Exception as e:
        logger.error(f"导出过程中发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='导出统计数据到飞书互动表格')
    parser.add_argument('--dimension', type=str, choices=['daily', 'weekly', 'monthly', 'quarterly'], 
                        help='指定导出的时间维度，不指定则导出所有维度')
    parser.add_argument('--batch-size', type=int, default=100, 
                        help='每批处理的记录数量，默认100')
    parser.add_argument('--verify', action='store_true',
                        help='仅验证AI擦除任务统计，不执行导出')
    args = parser.parse_args()
    
    # 配置日志
    os.makedirs("logs", exist_ok=True)
    logger.add(
        "logs/export_feishu.log",
        rotation="1 day",
        retention="7 days",
        level="INFO",
        encoding="utf-8"
    )
    
    # 如果指定了验证选项，只执行验证
    if args.verify:
        logger.info("开始验证AI擦除任务统计...")
        verify_erase_task_count()
        logger.info("验证完成")
        sys.exit(0)
    
    # 获取批处理大小，优先使用命令行参数，其次使用环境变量
    batch_size = args.batch_size
    if not batch_size:
        batch_size = int(os.getenv("FEISHU_BATCH_SIZE", "100"))
    
    # 运行导出
    dimension_name = args.dimension if args.dimension else "所有"
    logger.info(f"开始导出 {dimension_name} 维度数据到飞书互动表格...")
    
    success = export_stats_to_feishu(batch_size=batch_size, dimension=args.dimension)
    
    if success:
        logger.info("数据导出成功！")
    else:
        logger.error("数据导出失败！")
