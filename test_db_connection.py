import sys
import psycopg2
from urllib.parse import quote_plus
import os
import time
from loguru import logger

# 配置日志
logger.remove()
logger.add(sys.stdout, level="INFO")

def test_connection():
    """测试数据库连接"""
    # 数据库配置
    db_config = {
        "host": "vlc-hb-blueai-xy.pgdb.domob-inc.com",
        "port": 5432,
        "database": "ai_video_producer_pro",
        "user": "ai_video_producer_pro_ro",
        "password": "n7OvPC33o3nR"
    }
    
    logger.info("测试数据库连接...")
    logger.info(f"连接信息: {db_config['host']}:{db_config['port']}/{db_config['database']} (用户: {db_config['user']})")
    
    try:
        # 尝试连接
        conn = psycopg2.connect(
            host=db_config["host"],
            port=db_config["port"],
            database=db_config["database"],
            user=db_config["user"],
            password=db_config["password"],
            connect_timeout=10  # 10秒连接超时
        )
        
        # 尝试执行简单查询
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        logger.info(f"数据库连接成功！PostgreSQL 版本: {version[0]}")
        
        # 关闭连接
        cursor.close()
        conn.close()
        logger.info("数据库连接已关闭")
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_connection() 