import os
import sys
from datetime import datetime, timedelta
from loguru import logger
from tqdm import tqdm
import argparse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scripts.export_stats import StatsExporter
from src.services.stats_service import StatsService

def verify_erase_task_count():
    """验证AI擦除任务的计数是否正确"""
    try:
        # 创建统计服务实例
        stats_service = StatsService()
        
        # 收集历史日统计数据
        logger.info("收集历史日统计数据...")
        daily_stats = stats_service.collect_historical_daily_stats()
        
        # 分析擦除任务统计数据
        total_erase_tasks = 0
        
        for stat in daily_stats:
            total_erase_tasks += stat.erase_tasks
        
        logger.info("AI擦除任务统计验证结果:")
        logger.info(f"总记录数: {len(daily_stats)}")
        logger.info(f"AI擦除任务总数: {total_erase_tasks}")
        
        # 检查是否存在异常大的数值
        if total_erase_tasks > 10000:
            logger.warning(f"⚠️ AI擦除任务总数异常大: {total_erase_tasks}，可能存在重复计算")
        elif total_erase_tasks < 100 and len(daily_stats) > 0:
            logger.warning(f"⚠️ AI擦除任务总数异常小: {total_erase_tasks}，可能存在计算问题")
        else:
            logger.info("✅ AI擦除任务总数在合理范围内")
            
        return True
    except Exception as e:
        logger.error(f"验证过程中发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def export_stats_to_excel(dimension=None):
    """将统计数据导出到Excel文件
    
    Args:
        dimension: 指定导出的时间维度，为None时导出所有维度
        
    Returns:
        bool: 导出是否成功
    """
    try:
        # 创建导出器实例
        export_dir = "exports"
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
        exporter = StatsExporter(export_dir=export_dir)
        
        # 创建统计服务实例
        stats_service = StatsService()
        
        export_files = []
        
        # 根据指定的维度收集并导出数据
        if not dimension or dimension == 'daily':
            # 收集历史日统计数据
            logger.info("收集历史日统计数据...")
            daily_stats = stats_service.collect_historical_daily_stats()
            logger.info(f"收集到 {len(daily_stats)} 条日统计数据")
        
            # 导出日数据
            logger.info("导出日统计数据到Excel...")
            daily_file = exporter.export_stats(
            daily_stats,
                file_name="daily_stats.xlsx"
            )
            export_files.append(("日统计", daily_file))
            logger.info(f"日统计数据已导出到: {daily_file}")
        
        if not dimension or dimension == 'weekly':
            # 收集历史周统计数据
            logger.info("收集历史周统计数据...")
            weekly_stats = stats_service.collect_historical_weekly_stats()
            logger.info(f"收集到 {len(weekly_stats)} 条周统计数据")
        
            # 导出周数据
            logger.info("导出周统计数据到Excel...")
            weekly_file = exporter.export_stats(
                weekly_stats,
                file_name="weekly_stats.xlsx"
            )
            export_files.append(("周统计", weekly_file))
            logger.info(f"周统计数据已导出到: {weekly_file}")
        
        if not dimension or dimension == 'monthly':
            # 收集历史月统计数据
            logger.info("收集历史月统计数据...")
            monthly_stats = stats_service.collect_historical_monthly_stats()
            logger.info(f"收集到 {len(monthly_stats)} 条月统计数据")
        
            # 导出月数据
            logger.info("导出月统计数据到Excel...")
            monthly_file = exporter.export_stats(
                monthly_stats,
                file_name="monthly_stats.xlsx"
        )
            export_files.append(("月统计", monthly_file))
            logger.info(f"月统计数据已导出到: {monthly_file}")
        
        if not dimension or dimension == 'quarterly':
            # 收集历史季度统计数据
            logger.info("收集历史季度统计数据...")
            quarterly_stats = stats_service.collect_historical_quarterly_stats()
            logger.info(f"收集到 {len(quarterly_stats)} 条季度统计数据")
        
            # 导出季度数据
            logger.info("导出季度统计数据到Excel...")
            quarterly_file = exporter.export_stats(
                quarterly_stats,
                file_name="quarterly_stats.xlsx"
            )
            export_files.append(("季度统计", quarterly_file))
            logger.info(f"季度统计数据已导出到: {quarterly_file}")
        
        # 打印导出结果摘要
        logger.info("导出完成，文件列表:")
        for label, file_path in export_files:
            file_size = os.path.getsize(file_path) / 1024
            logger.info(f"- {label}: {file_path} ({file_size:.2f}KB)")
        
        return len(export_files) > 0
        
    except Exception as e:
        logger.error(f"导出过程中发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='导出统计数据到Excel文件')
    parser.add_argument('--dimension', type=str, choices=['daily', 'weekly', 'monthly', 'quarterly'], 
                        help='指定导出的时间维度，不指定则导出所有维度')
    parser.add_argument('--verify', action='store_true',
                        help='仅验证AI擦除任务统计，不执行导出')
    args = parser.parse_args()
    
    # 配置日志
    os.makedirs("logs", exist_ok=True)
    logger.add(
        "logs/export_excel.log",
        rotation="1 day",
        retention="7 days",
        level="INFO",
        encoding="utf-8"
    )
    
    # 如果指定了验证选项，只执行验证
    if args.verify:
        logger.info("开始验证AI擦除任务统计...")
        verify_erase_task_count()
        logger.info("验证完成")
        sys.exit(0)
    
    # 运行导出
    dimension_name = args.dimension if args.dimension else "所有"
    logger.info(f"开始导出 {dimension_name} 维度数据到Excel文件...")
    
    success = export_stats_to_excel(dimension=args.dimension)
    
    if success:
        logger.info("数据导出成功！")
    else:
        logger.error("数据导出失败！")
