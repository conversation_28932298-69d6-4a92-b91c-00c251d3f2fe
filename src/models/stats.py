from sqlalchemy import Column, Integer, String, DateTime, Float, JSON
from ..core.database import Base
from datetime import datetime

class VideoStats(Base):
    """视频统计数据模型"""
    __tablename__ = "video_stats"

    id = Column(Integer, primary_key=True, index=True)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    department = Column(String, nullable=False)
    project = Column(String, nullable=False)
    client = Column(String, nullable=False)
    dimension = Column(String, default="daily")  # 数据维度：daily, weekly, monthly, quarterly
    
    # AI任务统计
    erase_tasks = Column(Integer, default=0)  # AI擦除任务数（TASK_ERASE任务总数）
    face_swap_tasks = Column(Integer, default=0)  # AI换脸任务数
    face_swap_videos = Column(Integer, default=0)  # AI换脸-视频数
    video_analysis_tasks = Column(Integer, default=0)  # AI视频解析任务数
    text_to_image_tasks = Column(Integer, default=0)  # AI文生图任务数
    image_to_video_tasks = Column(Integer, default=0)  # AI图生视频任务数
    script_creation_tasks = Column(Integer, default=0)  # 创建脚本数
    
    # 素材和混剪统计
    total_materials = Column(Integer, default=0)  # 素材数
    total_mix_projects = Column(Integer, default=0)  # 混剪任务数
    mix_project_videos = Column(Integer, default=0)  # 混剪任务-生成视频数
    
    # AI配音和翻译
    ai_dubbing_tasks = Column(Integer, default=0)  # AI解说词配音-任务数
    original_translation_tasks = Column(Integer, default=0)  # 原声翻译-任务数
    
    # Agent任务统计
    social_media_agent_tasks = Column(Integer, default=0)  # 社媒Agent任务数
    social_media_agent_videos = Column(Integer, default=0)  # 社媒Agent-生成视频数
    ad_agent_tasks = Column(Integer, default=0)  # 广告Agent任务数
    ad_agent_videos = Column(Integer, default=0)  # 广告Agent-生成视频数
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow) 