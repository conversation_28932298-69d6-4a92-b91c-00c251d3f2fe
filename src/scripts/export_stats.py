import os
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import text
from ..core.database import get_db
from ..models.stats import VideoStats
from loguru import logger
from tqdm import tqdm
import pytz

class StatsExporter:
    def __init__(self, export_dir="exported"):
        """初始化导出器
        
        Args:
            export_dir (str): 导出文件保存目录
        """
        self.export_dir = export_dir
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)
            
        # 定义不同时间维度的覆盖窗口（天数）
        self.override_windows = {
            'daily': 30,     # 日数据覆盖最近30天
            'weekly': None,  # 周数据直接覆盖
            'monthly': None, # 月数据直接覆盖
            'quarterly': None # 季度数据直接覆盖
        }

    def _get_unique_key(self, stat):
        """生成数据唯一键
        
        Args:
            stat (VideoStats): 统计数据对象
            
        Returns:
            tuple: 唯一键元组 (start_time, end_time, project, department, client)
        """
        # 确保datetime对象是无时区的
        start_time = stat.start_time
        end_time = stat.end_time
        
        # 如果datetime对象带有时区，转换为无时区
        if hasattr(start_time, 'tzinfo') and start_time.tzinfo is not None:
            start_time = start_time.replace(tzinfo=None)
        if hasattr(end_time, 'tzinfo') and end_time.tzinfo is not None:
            end_time = end_time.replace(tzinfo=None)
            
        return (
            start_time.strftime("%Y-%m-%d %H:%M:%S"),
            end_time.strftime("%Y-%m-%d %H:%M:%S"),
            stat.project,
            stat.department,
            stat.client
        )

    def _get_time_dimension(self, stat):
        """判断数据的时间维度
        
        Args:
            stat (VideoStats): 统计数据对象
            
        Returns:
            str: 时间维度 ('daily', 'weekly', 'monthly', 'quarterly')
        """
        # 确保两个时间对象在相同的时区或都没有时区
        start_time = stat.start_time
        end_time = stat.end_time
        
        # 如果两个时间对象都有时区，则保持原样计算时间差
        # 如果只有一个有时区，或都没有时区，统一移除时区信息再计算
        if ((hasattr(start_time, 'tzinfo') and start_time.tzinfo is not None) and 
            (hasattr(end_time, 'tzinfo') and end_time.tzinfo is not None)):
            time_span = (end_time - start_time).days
        else:
            # 移除时区信息
            if hasattr(start_time, 'tzinfo') and start_time.tzinfo is not None:
                start_time = start_time.replace(tzinfo=None)
            if hasattr(end_time, 'tzinfo') and end_time.tzinfo is not None:
                end_time = end_time.replace(tzinfo=None)
            time_span = (end_time - start_time).days
        
        if time_span <= 1:
            return 'daily'
        elif time_span <= 7:
            return 'weekly'
        elif time_span <= 31:
            return 'monthly'
        else:
            return 'quarterly'

    def _load_existing_data(self, file_path):
        """加载已存在的Excel数据
        
        Args:
            file_path (str): Excel文件路径
            
        Returns:
            dict: 以唯一键为键的数据字典
        """
        if not os.path.exists(file_path):
            return {}
            
        df = pd.read_excel(file_path)
        existing_data = {}
        
        for _, row in df.iterrows():
            key = (
                row['开始时间'],
                row['结束时间'],
                row['项目'],
                row['部门'],
                row['客户']
            )
            existing_data[key] = row.to_dict()
            
        return existing_data

    def _should_override(self, stat):
        """判断是否应该覆盖历史数据
        
        Args:
            stat (VideoStats): 统计数据对象
            
        Returns:
            bool: 是否应该覆盖
        """
        # 获取数据的时间维度
        time_dimension = self._get_time_dimension(stat)
        
        # 获取对应维度的覆盖窗口
        override_days = self.override_windows[time_dimension]
        
        # 如果不是日数据，直接覆盖
        if override_days is None:
            return True
            
        # 对于日数据，检查是否在覆盖窗口期内
        now = datetime.now(pytz.timezone('Asia/Shanghai'))
        
        # 处理带时区的开始时间
        start_time = stat.start_time
        if hasattr(start_time, 'tzinfo') and start_time.tzinfo is None:
            # 如果没有时区信息，添加Asia/Shanghai时区
            tz = pytz.timezone('Asia/Shanghai')
            start_time = tz.localize(start_time)
        
        days_diff = (now - start_time).days
        return days_diff <= override_days

    def _filter_empty_stats(self, stats):
        """过滤掉所有字段都为空的记录
        
        Args:
            stats (list): 统计数据列表
            
        Returns:
            list: 过滤后的统计数据列表
        """
        filtered_stats = []
        for stat in stats:
            # 检查是否有任何非零数据
            has_data = any([
                # AI任务统计
                stat.erase_tasks > 0,  # AI擦除任务数
                stat.face_swap_tasks > 0,
                stat.face_swap_videos > 0,
                stat.video_analysis_tasks > 0,
                stat.text_to_image_tasks > 0,
                stat.image_to_video_tasks > 0,
                stat.script_creation_tasks > 0,
                # 素材和混剪统计
                stat.total_materials > 0,
                stat.total_mix_projects > 0,
                stat.mix_project_videos > 0,
                # AI配音和翻译
                stat.ai_dubbing_tasks > 0,
                stat.original_translation_tasks > 0
                # 移除Agent任务统计
            ])
            
            if has_data:
                filtered_stats.append(stat)
                
        return filtered_stats

    def _prepare_dataframe(self, stats, existing_data=None):
        """准备DataFrame数据
        
        Args:
            stats (list): 统计数据列表
            existing_data (dict, optional): 已存在的数据
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        data = []
        existing_data = existing_data or {}
        
        for stat in stats:
            # 确保datetime对象是无时区的
            start_time = stat.start_time
            end_time = stat.end_time
            
            # 如果datetime对象带有时区，转换为无时区
            if hasattr(start_time, 'tzinfo') and start_time.tzinfo is not None:
                start_time = start_time.replace(tzinfo=None)
            if hasattr(end_time, 'tzinfo') and end_time.tzinfo is not None:
                end_time = end_time.replace(tzinfo=None)
                
            key = (
                start_time.strftime("%Y-%m-%d %H:%M:%S"),
                end_time.strftime("%Y-%m-%d %H:%M:%S"),
                stat.project,
                stat.department,
                stat.client
            )
            
            row_data = {
                '开始时间': start_time,  # 这里使用无时区的datetime对象
                '结束时间': end_time,    # 这里使用无时区的datetime对象
                '部门': stat.department,
                '项目': stat.project,
                '客户': stat.client,
                # AI任务统计 - 只保留合并后的擦除任务数字段
                'AI擦除任务数': stat.erase_tasks,
                'AI换脸任务数': stat.face_swap_tasks,
                'AI换脸-视频数': stat.face_swap_videos,
                'AI视频解析任务数': stat.video_analysis_tasks,
                'AI文生图任务数': stat.text_to_image_tasks,
                'AI图生视频任务数': stat.image_to_video_tasks,
                '创建脚本数': stat.script_creation_tasks,
                # 素材和混剪统计
                '素材数': stat.total_materials,
                '混剪任务数': stat.total_mix_projects,
                '混剪任务-生成视频数': stat.mix_project_videos,
                # AI配音和翻译
                'AI解说词配音-任务数': stat.ai_dubbing_tasks,
                '原声翻译-任务数': stat.original_translation_tasks,
                # 移除Agent任务统计字段
                '更新时间': datetime.now()  # 使用无时区的当前时间
            }
            
            # 检查是否需要覆盖已存在的数据
            if key in existing_data:
                if self._should_override(stat):
                    logger.debug(f"覆盖数据: {key}")
                    data.append(row_data)
                else:
                    logger.debug(f"跳过覆盖: {key}")
                    data.append(existing_data[key])
            else:
                logger.debug(f"新增数据: {key}")
                data.append(row_data)
                
        return pd.DataFrame(data)

    def export_stats(self, stats, file_name=None, show_progress=True):
        """导出统计数据到Excel文件
        
        Args:
            stats (list): 统计数据列表
            file_name (str, optional): 导出文件名，默认为当前时间
            show_progress (bool): 是否显示进度条
            
        Returns:
            str: 导出文件路径
        """
        if not file_name:
            file_name = f"stats_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
        file_path = os.path.join(self.export_dir, file_name)
        
        # 过滤空数据
        if show_progress:
            logger.info("正在过滤空数据...")
            stats = self._filter_empty_stats(stats)
            logger.info(f"过滤后剩余 {len(stats)} 条数据")
        
        # 加载已存在的数据
        if show_progress:
            logger.info("正在加载已存在的数据...")
        existing_data = self._load_existing_data(file_path)
        logger.info(f"加载到 {len(existing_data)} 条已存在数据")
        
        # 准备数据
        if show_progress:
            logger.info("正在准备数据...")
        df = self._prepare_dataframe(stats, existing_data)
        
        # 保存到Excel
        if show_progress:
            logger.info(f"正在导出数据到 {file_path}...")
        df.to_excel(file_path, index=False)
        
        if show_progress:
            logger.info(f"数据导出完成！文件大小: {os.path.getsize(file_path)/1024:.2f}KB")
            logger.info(f"总记录数: {len(df)}")
            
        return file_path

def main():
    """主函数"""
    from ..services.stats_service import StatsService
    
    # 创建导出器
    exporter = StatsExporter()
    
    # 创建统计服务
    stats_service = StatsService()
    
    # 收集历史日统计数据
    logger.info("开始收集历史日统计数据...")
    daily_stats = stats_service.collect_historical_daily_stats()
    logger.info(f"收集到 {len(daily_stats)} 条日统计数据")
    
    # 收集周数据
    logger.info("开始收集周统计数据...")
    weekly_stats = stats_service.collect_weekly_stats()
    logger.info(f"收集到 {len(weekly_stats)} 条周统计数据")
    
    # 收集月数据
    logger.info("开始收集月统计数据...")
    monthly_stats = stats_service.collect_monthly_stats()
    logger.info(f"收集到 {len(monthly_stats)} 条月统计数据")
    
    # 收集季度数据
    logger.info("开始收集季度统计数据...")
    quarterly_stats = stats_service.collect_quarterly_stats()
    logger.info(f"收集到 {len(quarterly_stats)} 条季度统计数据")
    
    # 导出数据
    logger.info("开始导出数据...")
    
    # 导出日数据
    daily_export_path = exporter.export_stats(
        daily_stats,
        file_name="daily_stats.xlsx"
    )
    logger.info(f"日数据已导出到: {daily_export_path}")
    
    # 导出周数据
    weekly_export_path = exporter.export_stats(
        weekly_stats,
        file_name="weekly_stats.xlsx"
    )
    logger.info(f"周数据已导出到: {weekly_export_path}")
    
    # 导出月数据
    monthly_export_path = exporter.export_stats(
        monthly_stats,
        file_name="monthly_stats.xlsx"
    )
    logger.info(f"月数据已导出到: {monthly_export_path}")
    
    # 导出季度数据
    quarterly_export_path = exporter.export_stats(
        quarterly_stats,
        file_name="quarterly_stats.xlsx"
    )
    logger.info(f"季度数据已导出到: {quarterly_export_path}")
    
    logger.info("所有数据导出完成！")

if __name__ == "__main__":
    main() 