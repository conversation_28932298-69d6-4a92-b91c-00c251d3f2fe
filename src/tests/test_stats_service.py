import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy import text, column
from src.services.stats_service import StatsService
from src.models.stats import VideoStats
from src.core.database import get_db

@pytest.fixture
def stats_service():
    """创建统计服务实例"""
    service = StatsService()
    service.feishu_service = Mock()
    return service

@pytest.fixture
def db_session():
    """提供数据库会话"""
    with get_db() as session:
        yield session

def test_collect_daily_stats(stats_service, db_session):
    """测试收集日统计数据"""
    # 获取昨天的日期范围
    end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = end_time - timedelta(days=1)
    
    # 执行统计查询
    result = stats_service._collect_stats(start_time, end_time)
    
    # 验证结果
    assert isinstance(result, list)
    for stat in result:
        assert isinstance(stat, VideoStats)
        assert stat.start_time == start_time
        assert stat.end_time == end_time
        assert stat.total_tasks >= 0
        assert stat.completed_tasks >= 0
        assert stat.failed_tasks >= 0
        assert stat.total_materials >= 0
        assert stat.processed_materials >= 0
        assert stat.total_mix_projects >= 0
        assert stat.completed_mix_projects >= 0

def test_collect_weekly_stats(stats_service, db_session):
    """测试收集周统计数据"""
    # 获取上周的日期范围
    end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = end_time - timedelta(days=7)
    
    # 执行统计查询
    result = stats_service._collect_stats(start_time, end_time)
    
    # 验证结果
    assert isinstance(result, list)
    for stat in result:
        assert isinstance(stat, VideoStats)
        assert stat.start_time == start_time
        assert stat.end_time == end_time
        assert stat.total_tasks >= 0
        assert stat.completed_tasks >= 0
        assert stat.failed_tasks >= 0

def test_collect_monthly_stats(stats_service, db_session):
    """测试收集月统计数据"""
    # 获取上月的日期范围
    end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = end_time - timedelta(days=30)
    
    # 执行统计查询
    result = stats_service._collect_stats(start_time, end_time)
    
    # 验证结果
    assert isinstance(result, list)
    for stat in result:
        assert isinstance(stat, VideoStats)
        assert stat.start_time == start_time
        assert stat.end_time == end_time
        assert stat.total_tasks >= 0
        assert stat.completed_tasks >= 0
        assert stat.failed_tasks >= 0

def test_collect_quarterly_stats(stats_service, db_session):
    """测试收集季度统计数据"""
    # 获取上季度的日期范围
    end_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = end_time - timedelta(days=90)
    
    # 执行统计查询
    result = stats_service._collect_stats(start_time, end_time)
    
    # 验证结果
    assert isinstance(result, list)
    for stat in result:
        assert isinstance(stat, VideoStats)
        assert stat.start_time == start_time
        assert stat.end_time == end_time
        assert stat.total_tasks >= 0
        assert stat.completed_tasks >= 0
        assert stat.failed_tasks >= 0

def test_real_database_queries(stats_service, db_session):
    """测试实际数据库查询"""
    # 测试项目配置查询
    project_configs = db_session.execute(
        text("SELECT project_id, project_name, team_group_info FROM project_config LIMIT 1")
    ).fetchall()
    assert len(project_configs) > 0

    # 测试任务统计查询
    task_stats = db_session.execute(
        text("""
            SELECT 
                COUNT(*) as total_tasks,
                SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
                SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks
            FROM base_task
            WHERE create_time >= NOW() - INTERVAL '1 day'
        """)
    ).fetchone()
    assert task_stats is not None
    assert task_stats.total_tasks >= 0
    assert task_stats.completed_tasks >= 0
    assert task_stats.failed_tasks >= 0

    # 测试素材统计查询
    material_stats = db_session.execute(
        text("""
            SELECT 
                COUNT(*) as total_materials,
                SUM(CASE WHEN deepspace_index_status = 'SUCCESS' THEN 1 ELSE 0 END) as processed_materials
            FROM material
            WHERE created_time >= NOW() - INTERVAL '1 day'
        """)
    ).fetchone()
    assert material_stats is not None
    assert material_stats.total_materials >= 0
    assert material_stats.processed_materials >= 0

def test_database_error_handling(stats_service):
    """测试数据库错误处理"""
    # 模拟数据库错误
    with patch('src.services.stats_service.get_db') as mock_get_db:
        mock_get_db.side_effect = Exception("数据库连接错误")
        
        # 验证错误处理
        with pytest.raises(Exception) as exc_info:
            stats_service.collect_daily_stats()
        assert "数据库连接错误" in str(exc_info.value) 