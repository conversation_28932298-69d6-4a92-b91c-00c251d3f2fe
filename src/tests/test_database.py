import pytest
from sqlalchemy import text
from src.core.database import get_db, init_db
from src.core.config import DB_CONFIG

def test_database_connection():
    """测试数据库连接"""
    try:
        with get_db() as db:
            # 执行简单查询测试连接
            result = db.execute(text("SELECT 1")).scalar()
            assert result == 1
    except Exception as e:
        pytest.fail(f"数据库连接失败: {str(e)}")

def test_database_tables():
    """测试关键表是否存在"""
    required_tables = [
        "base_task",
        "material",
        "mix_project",
        "project_config"
    ]
    
    try:
        with get_db() as db:
            for table in required_tables:
                # 检查表是否存在
                result = db.execute(
                    text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = :table)"),
                    {"table": table}
                ).scalar()
                assert result, f"表 {table} 不存在"
    except Exception as e:
        pytest.fail(f"表检查失败: {str(e)}")

def test_database_permissions():
    """测试数据库读取权限"""
    try:
        with get_db() as db:
            # 测试读取权限
            db.execute(text("SELECT * FROM base_task LIMIT 1"))
            db.execute(text("SELECT * FROM material LIMIT 1"))
            db.execute(text("SELECT * FROM mix_project LIMIT 1"))
            db.execute(text("SELECT * FROM project_config LIMIT 1"))
            
    except Exception as e:
        pytest.fail(f"数据库读取权限测试失败: {str(e)}")

def test_database_config():
    """测试数据库配置"""
    assert DB_CONFIG["host"], "数据库主机未配置"
    assert DB_CONFIG["port"], "数据库端口未配置"
    assert DB_CONFIG["database"], "数据库名称未配置"
    assert DB_CONFIG["user"], "数据库用户名未配置"
    assert DB_CONFIG["password"], "数据库密码未配置" 