from datetime import datetime, timedelta, timezone
from sqlalchemy import func, and_, or_, case, text, column
from ..core.database import get_db
from ..models.stats import VideoStats
from loguru import logger
import pytz
from tqdm import tqdm
import json
import traceback

class StatsService:
    def __init__(self):
        self.feishu_service = None  # 将在主程序中初始化
        self.tz = pytz.timezone('Asia/Shanghai')  # 设置时区为UTC+8

    def _convert_to_utc(self, dt):
        """将本地时间转换为UTC时间"""
        return dt.astimezone(timezone.utc)

    def _convert_from_utc(self, dt):
        """将UTC时间转换为本地时间"""
        return dt.astimezone(self.tz)

    def _get_time_range(self, days_ago=0):
        """获取指定天数的UTC+8时区时间范围
        
        Args:
            days_ago (int): 多少天前
            
        Returns:
            tuple: (start_time, end_time) UTC+8时区的时间范围
        """
        now = datetime.now(self.tz)
        end_time = now.replace(hour=23, minute=59, second=59, microsecond=999999) - timedelta(days=days_ago)
        start_time = end_time.replace(hour=0, minute=0, second=0, microsecond=0)
        return start_time, end_time

    def collect_daily_stats(self, days_ago=0):
        """收集每日统计数据
        
        Args:
            days_ago (int): 多少天前的数据，默认0表示今天
        """
        start_time, end_time = self._get_time_range(days_ago)
        return self._collect_stats(start_time, end_time)

    def collect_historical_daily_stats(self, months=6):
        """收集历史日统计数据
        
        Args:
            months (int): 收集多少个月的历史数据，默认6个月
            
        Returns:
            list: 历史统计数据列表
        """
        try:
            with get_db() as db:
                # 计算时间范围
                end_time = datetime.now(self.tz)
                start_time = end_time - timedelta(days=months * 30)
                
                # 获取所有项目配置信息
                project_configs = db.execute(
                    text("SELECT project_id, project_name, team_group_info FROM project_config")
                ).fetchall()
                
                # 获取所有项目ID
                project_ids = [p.project_id for p in project_configs]
                
                # 批量查询所有时间范围的任务数据
                task_stats = db.execute(
                    text("""
                        WITH RECURSIVE dates AS (
                            SELECT 
                                generate_series(
                                    date_trunc('day', :start_time AT TIME ZONE 'Asia/Shanghai'),
                                    date_trunc('day', :end_time AT TIME ZONE 'Asia/Shanghai'),
                                    '1 day'::interval
                                )::date AS date
                        ),
                        task_stats AS (
                            SELECT 
                                project_id,
                                date_trunc('day', create_time AT TIME ZONE 'Asia/Shanghai')::date AS date,
                                COUNT(*) as total_tasks,
                                SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as completed_tasks,
                                SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) as failed_tasks,
                                COUNT(CASE WHEN status = 'PROCESSING' THEN 1 END) as processing_tasks,
                                COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_tasks,
                                -- AI任务统计 - 只统计成功的任务
                                COUNT(CASE WHEN task_type = 'TASK_ERASE' AND status = 'SUCCESS' THEN 1 END) as erase_tasks,
                                COUNT(CASE WHEN (task_type = 'TASK_FACE_SWAP' OR task_type = 'TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' THEN 1 END) as face_swap_tasks,
                                COUNT(CASE WHEN (task_type = 'TASK_FACE_SWAP' OR task_type = 'TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' THEN 1 END) as face_swap_videos,
                                COUNT(CASE WHEN task_type = 'TASK_STORY_BOARD_ANA' AND status = 'SUCCESS' THEN 1 END) as video_analysis_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_MJ_TXT2IMG' AND status = 'SUCCESS' THEN 1 END) as text_to_image_tasks,
                                COUNT(CASE WHEN (task_type = 'TASK_IMG2VIDEO' OR task_type = 'TASK_IMG2VIDEO_MINIMAX' OR task_type = 'TASK_IMG2VIDEO_VIDU') AND status = 'SUCCESS' THEN 1 END) as image_to_video_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_AI_SCRIPT' AND status = 'SUCCESS' THEN 1 END) as script_creation_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_DUBBING' AND status = 'SUCCESS' THEN 1 END) as ai_dubbing_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_ASR' AND status = 'SUCCESS' THEN 1 END) as original_translation_tasks,
                                -- Agent任务统计
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH_V1' THEN 1 END) as social_media_agent_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH_V1' THEN 1 END) as social_media_agent_videos,
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH' THEN 1 END) as ad_agent_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH' THEN 1 END) as ad_agent_videos
                            FROM base_task bt
                            WHERE project_id = ANY(:project_ids)
                                AND create_time >= :start_time
                                AND create_time <= :end_time
                            GROUP BY project_id, date
                        )
                        SELECT * FROM task_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询所有时间范围的素材数据
                material_stats = db.execute(
                    text("""
                        WITH RECURSIVE dates AS (
                            SELECT 
                                generate_series(
                                    date_trunc('day', :start_time AT TIME ZONE 'Asia/Shanghai'),
                                    date_trunc('day', :end_time AT TIME ZONE 'Asia/Shanghai'),
                                    '1 day'::interval
                                )::date AS date
                        ),
                        material_stats AS (
                            SELECT 
                                project_id,
                                date_trunc('day', created_time AT TIME ZONE 'Asia/Shanghai')::date AS date,
                                COUNT(*) as total_materials,
                                SUM(CASE WHEN deepspace_index_status = 'SUCCESS' THEN 1 ELSE 0 END) as processed_materials
                            FROM material
                            WHERE project_id = ANY(:project_ids)
                                AND created_time >= :start_time
                                AND created_time <= :end_time
                                AND delete_status = 'NORMAL'
                            GROUP BY project_id, date
                        )
                        SELECT * FROM material_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询所有时间范围的混剪项目数据
                mix_project_stats = db.execute(
                    text("""
                        WITH RECURSIVE dates AS (
                            SELECT 
                                generate_series(
                                    date_trunc('day', :start_time AT TIME ZONE 'Asia/Shanghai'),
                                    date_trunc('day', :end_time AT TIME ZONE 'Asia/Shanghai'),
                                    '1 day'::interval
                                )::date AS date
                        ),
                        mix_project_stats AS (
                            SELECT 
                                project_id,
                                date_trunc('day', create_time AT TIME ZONE 'Asia/Shanghai')::date AS date,
                                COUNT(*) as total_mix_projects,
                                SUM(CASE WHEN delete_status = 'NORMAL' THEN 1 ELSE 0 END) as completed_mix_projects,
                                0 as mix_project_videos
                            FROM mix_project
                            WHERE project_id = ANY(:project_ids)
                                AND create_time >= :start_time
                                AND create_time <= :end_time
                                AND delete_status = 'NORMAL'
                            GROUP BY project_id, date
                        )
                        SELECT * FROM mix_project_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询所有时间范围的混剪成片数据
                mix_artwork_stats = db.execute(
                    text("""
                        WITH RECURSIVE dates AS (
                            SELECT 
                                generate_series(
                                    date_trunc('day', :start_time AT TIME ZONE 'Asia/Shanghai'),
                                    date_trunc('day', :end_time AT TIME ZONE 'Asia/Shanghai'),
                                    '1 day'::interval
                                )::date AS date
                        ),
                        mix_artwork_stats AS (
                            SELECT 
                                mp.project_id,
                                date_trunc('day', mp.create_time AT TIME ZONE 'Asia/Shanghai')::date AS date,
                                COUNT(ma.mix_artwork_id) as mix_project_videos
                            FROM mix_artwork ma
                            JOIN mix_project mp ON ma.mix_project_id = mp.mix_project_id
                            WHERE mp.project_id = ANY(:project_ids)
                                AND ma.artwork_vlc_id IS NOT NULL
                                AND mp.create_time >= :start_time
                                AND mp.create_time <= :end_time
                                AND mp.delete_status = 'NORMAL'
                            GROUP BY mp.project_id, date
                        )
                        SELECT * FROM mix_artwork_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询TTS配置数据（解说词配音）
                tts_stats = db.execute(
                    text("""
                        WITH RECURSIVE dates AS (
                            SELECT 
                                generate_series(
                                    date_trunc('day', :start_time AT TIME ZONE 'Asia/Shanghai'),
                                    date_trunc('day', :end_time AT TIME ZONE 'Asia/Shanghai'),
                                    '1 day'::interval
                                )::date AS date
                        ),
                        tts_stats AS (
                            SELECT 
                                mp.project_id,
                                date_trunc('day', mp.create_time AT TIME ZONE 'Asia/Shanghai')::date AS date,
                                COUNT(DISTINCT CASE 
                                    WHEN sc.tts::text LIKE '%voice_id%' AND sc.tts::text LIKE '%tts_id%' 
                                    THEN s.shooting_id 
                                END) as tts_configs
                            FROM shooting s
                            JOIN mix_project mp ON s.mix_project_id = mp.mix_project_id
                            JOIN shooting_config sc ON s.shooting_config_id = sc.shooting_config_id
                            WHERE mp.project_id = ANY(:project_ids)
                                AND mp.create_time >= :start_time
                                AND mp.create_time <= :end_time
                                AND mp.delete_status = 'NORMAL'
                            GROUP BY mp.project_id, date
                        )
                        SELECT * FROM tts_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 将查询结果转换为字典，方便查找
                task_stats_dict = {(t.project_id, t.date): t for t in task_stats} if task_stats else {}
                material_stats_dict = {(m.project_id, m.date): m for m in material_stats} if material_stats else {}
                mix_project_stats_dict = {(m.project_id, m.date): m for m in mix_project_stats} if mix_project_stats else {}
                mix_artwork_stats_dict = {(m.project_id, m.date): m for m in mix_artwork_stats} if mix_artwork_stats else {}
                tts_stats_dict = {(t.project_id, t.date): t for t in tts_stats} if tts_stats else {}
                
                # 创建空统计对象，用于安全访问属性
                class EmptyStat:
                    def __getattr__(self, _):
                        return 0
                empty_stat = EmptyStat()
                
                # 生成所有日期
                all_dates = [
                    start_time + timedelta(days=i)
                    for i in range((end_time - start_time).days + 1)
                ]
                
                # 按项目和日期组织数据
                stats = []
                for project in project_configs:
                    project_id = project.project_id
                    project_name = project.project_name
                    team_info = {}
                    
                    # 安全地解析 team_group_info
                    if project.team_group_info:
                        try:
                            if isinstance(project.team_group_info, str):
                                team_info = json.loads(project.team_group_info)
                            else:
                                team_info = project.team_group_info
                        except Exception as e:
                            logger.error(f"Failed to parse team_group_info for project {project_id}: {str(e)}")
                            team_info = {}
                    
                    for date in all_dates:
                        date_key = date.date()
                        
                        # 获取该项目的统计数据
                        task_stat = task_stats_dict.get((project_id, date_key))
                        material_stat = material_stats_dict.get((project_id, date_key))
                        mix_project_stat = mix_project_stats_dict.get((project_id, date_key))
                        mix_artwork_stat = mix_artwork_stats_dict.get((project_id, date_key))
                        tts_stat = tts_stats_dict.get((project_id, date_key))
                        
                        # 检查是否有数据
                        if not self._has_data(task_stat, material_stat, mix_project_stat, mix_artwork_stat):
                            continue
                        
                        # 安全获取部门和客户信息
                        department = '个人空间'
                        client = '未知客户'
                        if team_info and isinstance(team_info, dict):
                            team_detail = team_info.get('team_detail', {})
                            if isinstance(team_detail, dict):
                                department = team_detail.get('team_name', '个人空间')
                            
                            client_detail = team_info.get('client_detail', {})
                            if isinstance(client_detail, dict):
                                client = client_detail.get('client_name', '未知客户')
                        
                        # 创建统计记录
                        record = VideoStats(
                            start_time=date,
                            end_time=date.replace(hour=23, minute=59, second=59, microsecond=999999),
                            department=department,
                            project=project_name or '未知项目',
                            client=client,
                            # 添加维度标识
                            dimension="daily",
                            # AI任务统计 - 直接使用总数
                            erase_tasks=task_stat.erase_tasks if task_stat else 0,
                            face_swap_tasks=task_stat.face_swap_tasks if task_stat else 0,
                            face_swap_videos=task_stat.face_swap_videos if task_stat else 0,
                            video_analysis_tasks=task_stat.video_analysis_tasks if task_stat else 0,
                            text_to_image_tasks=task_stat.text_to_image_tasks if task_stat else 0,
                            image_to_video_tasks=task_stat.image_to_video_tasks if task_stat else 0,
                            script_creation_tasks=task_stat.script_creation_tasks if task_stat else 0,
                            # 其他任务统计
                            total_materials=material_stat.total_materials if material_stat else 0,
                            total_mix_projects=mix_project_stat.total_mix_projects if mix_project_stat else 0,
                            mix_project_videos=mix_artwork_stat.mix_project_videos if mix_artwork_stat else 0,
                            # AI配音和翻译
                            ai_dubbing_tasks=tts_stat.tts_configs if tts_stat else 0,
                            original_translation_tasks=task_stat.original_translation_tasks if task_stat else 0,
                            # Agent任务统计
                            social_media_agent_tasks=task_stat.social_media_agent_tasks if task_stat else 0,
                            social_media_agent_videos=task_stat.social_media_agent_videos if task_stat else 0,
                            ad_agent_tasks=task_stat.ad_agent_tasks if task_stat else 0,
                            ad_agent_videos=task_stat.ad_agent_videos if task_stat else 0
                        )
                        stats.append(record)
                
                logger.info(f"历史数据收集完成，共收集到 {len(stats)} 条记录")
                return stats
                
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to collect historical stats: {str(e)}")
            return []

    def _aggregate_stats_by_department(self, stats_list):
        """将统计数据按部门整合
        
        Args:
            stats_list (list): 统计数据列表
            
        Returns:
            list: 按部门整合后的统计数据列表
        """
        if not stats_list:
            return []
            
        # 按部门和时间范围进行分组
        grouped_stats = {}
        for stat in stats_list:
            # 用 (部门, 开始时间, 结束时间, 维度) 作为键
            key = (stat.department, stat.start_time.strftime("%Y-%m-%d"), 
                   stat.end_time.strftime("%Y-%m-%d"), stat.dimension)
            
            if key not in grouped_stats:
                # 创建一个新的统计对象作为聚合基础
                agg_stat = VideoStats(
                    start_time=stat.start_time,
                    end_time=stat.end_time,
                    department=stat.department,
                    project="",  # 项目设为空
                    client="",   # 客户设为空
                    dimension=stat.dimension,
                    erase_tasks=stat.erase_tasks,
                    face_swap_tasks=stat.face_swap_tasks,
                    face_swap_videos=stat.face_swap_videos,
                    video_analysis_tasks=stat.video_analysis_tasks,
                    text_to_image_tasks=stat.text_to_image_tasks,
                    image_to_video_tasks=stat.image_to_video_tasks,
                    script_creation_tasks=stat.script_creation_tasks,
                    total_materials=stat.total_materials,
                    total_mix_projects=stat.total_mix_projects,
                    mix_project_videos=stat.mix_project_videos,
                    ai_dubbing_tasks=stat.ai_dubbing_tasks,
                    original_translation_tasks=stat.original_translation_tasks,
                    social_media_agent_tasks=stat.social_media_agent_tasks,
                    social_media_agent_videos=stat.social_media_agent_videos,
                    ad_agent_tasks=stat.ad_agent_tasks,
                    ad_agent_videos=stat.ad_agent_videos
                )
                grouped_stats[key] = agg_stat
            else:
                # 累加数据到已有的统计对象
                agg_stat = grouped_stats[key]
                agg_stat.erase_tasks += stat.erase_tasks
                agg_stat.face_swap_tasks += stat.face_swap_tasks
                agg_stat.face_swap_videos += stat.face_swap_videos
                agg_stat.video_analysis_tasks += stat.video_analysis_tasks
                agg_stat.text_to_image_tasks += stat.text_to_image_tasks
                agg_stat.image_to_video_tasks += stat.image_to_video_tasks
                agg_stat.script_creation_tasks += stat.script_creation_tasks
                agg_stat.total_materials += stat.total_materials
                agg_stat.total_mix_projects += stat.total_mix_projects
                agg_stat.mix_project_videos += stat.mix_project_videos
                agg_stat.ai_dubbing_tasks += stat.ai_dubbing_tasks
                agg_stat.original_translation_tasks += stat.original_translation_tasks
                agg_stat.social_media_agent_tasks += stat.social_media_agent_tasks
                agg_stat.social_media_agent_videos += stat.social_media_agent_videos
                agg_stat.ad_agent_tasks += stat.ad_agent_tasks
                agg_stat.ad_agent_videos += stat.ad_agent_videos
        
        # 转换回列表
        return list(grouped_stats.values())

    def collect_historical_weekly_stats(self, months=6):
        """收集历史周统计数据
        
        Args:
            months (int): 收集多少个月的历史数据，默认6个月
            
        Returns:
            list: 历史周统计数据列表
        """
        try:
            all_weekly_stats = []
            now = datetime.now(self.tz)
            
            # 计算开始时间（N个月前）
            start_time = now - timedelta(days=months * 30)
            
            # 调整开始时间到所在周的周一
            weekday = start_time.weekday()  # 0是周一，6是周日
            start_time = start_time - timedelta(days=weekday)
            start_time = start_time.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 当前时间
            end_time = now
            
            # 按周收集并聚合数据
            current_start = start_time
            while current_start < end_time:
                # 计算当周的结束日期（周日）
                current_end = current_start + timedelta(days=6)
                current_end = current_end.replace(hour=23, minute=59, second=59, microsecond=999999)
                
                # 如果结束时间超过当前时间，调整为当前时间
                if current_end > end_time:
                    current_end = end_time
                
                # 收集当周数据
                week_data = self._collect_stats(
                    current_start, 
                    current_end, 
                    dimension="weekly"
                )
                all_weekly_stats.extend(week_data)
                
                # 移动到下一周的周一
                current_start = current_start + timedelta(days=7)
            
            # 按部门整合统计数据
            aggregated_stats = self._aggregate_stats_by_department(all_weekly_stats)
            
            logger.info(f"历史周数据收集完成，收集到 {len(all_weekly_stats)} 条原始记录，整合为 {len(aggregated_stats)} 条按部门汇总的记录")
            return aggregated_stats
                
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to collect historical weekly stats: {str(e)}")
            return []

    def collect_historical_monthly_stats(self, months=6):
        """收集历史月统计数据
        
        Args:
            months (int): 收集多少个月的历史数据，默认6个月
            
        Returns:
            list: 历史月统计数据列表
        """
        try:
            all_monthly_stats = []
            now = datetime.now(self.tz)
            
            # 确定结束年月
            end_year, end_month = now.year, now.month
            
            # 计算起始年月
            start_month = end_month - months
            start_year = end_year
            while start_month <= 0:
                start_month += 12
                start_year -= 1
            
            # 收集所有月的数据
            current_year, current_month = start_year, start_month
            
            for _ in range(months + 1):
                # 如果超过当前月份，则退出循环
                if current_year > end_year or (current_year == end_year and current_month > end_month):
                    break
                    
                # 计算当月第一天和最后一天
                month_start = datetime(current_year, current_month, 1, tzinfo=self.tz)
                
                # 计算下月第一天
                if current_month == 12:
                    next_month = datetime(current_year + 1, 1, 1, tzinfo=self.tz)
                else:
                    next_month = datetime(current_year, current_month + 1, 1, tzinfo=self.tz)
                
                # 当月最后一天
                month_end = next_month - timedelta(seconds=1)
                month_end = month_end.replace(hour=23, minute=59, second=59, microsecond=999999)
                
                # 收集当月数据
                month_data = self._collect_stats(
                    month_start, 
                    month_end, 
                    dimension="monthly"
                )
                all_monthly_stats.extend(month_data)
                
                # 移动到下个月
                current_month += 1
                if current_month > 12:
                    current_month = 1
                    current_year += 1
            
            # 按部门整合统计数据
            aggregated_stats = self._aggregate_stats_by_department(all_monthly_stats)
            
            logger.info(f"历史月数据收集完成，收集到 {len(all_monthly_stats)} 条原始记录，整合为 {len(aggregated_stats)} 条按部门汇总的记录")
            return aggregated_stats
                
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to collect historical monthly stats: {str(e)}")
            return []

    def collect_historical_quarterly_stats(self, months=12):
        """收集历史季度统计数据
        
        Args:
            months (int): 收集多少个月的历史数据，默认12个月(约4个季度)
            
        Returns:
            list: 历史季度统计数据列表
        """
        try:
            all_quarterly_stats = []
            now = datetime.now(self.tz)
            
            # 确定开始和结束年季度
            end_year, end_month = now.year, now.month
            end_quarter = (end_month - 1) // 3 + 1
            
            # 计算起始年季度（约N个月前）
            start_time = now - timedelta(days=months * 30)
            start_year, start_month = start_time.year, start_time.month
            start_quarter = (start_month - 1) // 3 + 1
            
            # 收集所有季度的数据
            current_year, current_quarter = start_year, start_quarter
            
            while True:
                # 如果超过当前季度，则退出循环
                if current_year > end_year or (current_year == end_year and current_quarter > end_quarter):
                    break
                    
                # 计算当季度的起始月份
                quarter_start_month = (current_quarter - 1) * 3 + 1
                
                # 计算当季度的起始和结束日期
                quarter_start = datetime(current_year, quarter_start_month, 1, tzinfo=self.tz)
                
                # 计算下季度的第一天
                if quarter_start_month + 3 > 12:
                    next_quarter = datetime(current_year + 1, (quarter_start_month + 3) % 12 or 1, 1, tzinfo=self.tz)
                else:
                    next_quarter = datetime(current_year, quarter_start_month + 3, 1, tzinfo=self.tz)
                
                # 当季度最后一天
                quarter_end = next_quarter - timedelta(seconds=1)
                quarter_end = quarter_end.replace(hour=23, minute=59, second=59, microsecond=999999)
                
                # 收集当季度数据
                quarter_data = self._collect_stats(
                    quarter_start, 
                    quarter_end, 
                    dimension="quarterly"
                )
                all_quarterly_stats.extend(quarter_data)
                
                # 移动到下个季度
                current_quarter += 1
                if current_quarter > 4:
                    current_quarter = 1
                    current_year += 1
            
            # 按部门整合统计数据
            aggregated_stats = self._aggregate_stats_by_department(all_quarterly_stats)
            
            logger.info(f"历史季度数据收集完成，收集到 {len(all_quarterly_stats)} 条原始记录，整合为 {len(aggregated_stats)} 条按部门汇总的记录")
            return aggregated_stats
                
        except Exception as e:
            traceback.print_exc()
            logger.error(f"Failed to collect historical quarterly stats: {str(e)}")
            return []

    def collect_weekly_stats(self, weeks_ago=0):
        """收集每周统计数据（自然周：周一到周日）
        
        Args:
            weeks_ago (int): 多少周前的数据，默认0表示本周
        """
        now = datetime.now(self.tz)
        
        # 计算本周的周一和周日
        weekday = now.weekday()  # 0是周一，6是周日
        
        # 计算目标周的周一
        monday = (now - timedelta(days=weekday)) - timedelta(weeks=weeks_ago)
        monday = monday.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 计算目标周的周日
        sunday = monday + timedelta(days=6)
        sunday = sunday.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 添加维度标识，传递给_collect_stats方法
        return self._collect_stats(monday, sunday, dimension="weekly")

    def collect_monthly_stats(self, months_ago=0):
        """收集每月统计数据（自然月：1号到月末）
        
        Args:
            months_ago (int): 多少月前的数据，默认0表示本月
        """
        now = datetime.now(self.tz)
        
        # 计算目标月的年和月
        year = now.year
        month = now.month - months_ago
        
        # 处理月份溢出
        while month <= 0:
            year -= 1
            month += 12
        
        # 计算目标月的1号
        start_time = datetime(year, month, 1, tzinfo=self.tz)
        
        # 计算下个月的1号，然后减去1秒得到当月最后一天
        if month == 12:
            end_month = datetime(year + 1, 1, 1, tzinfo=self.tz) - timedelta(seconds=1)
        else:
            end_month = datetime(year, month + 1, 1, tzinfo=self.tz) - timedelta(seconds=1)
        
        # 设置为当天的23:59:59.999999
        end_time = end_month.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 添加维度标识，传递给_collect_stats方法
        return self._collect_stats(start_time, end_time, dimension="monthly")

    def collect_quarterly_stats(self, quarters_ago=0):
        """收集每季度统计数据（自然季度：1-3月，4-6月，7-9月，10-12月）
        
        Args:
            quarters_ago (int): 多少季度前的数据，默认0表示本季度
        """
        now = datetime.now(self.tz)
        
        # 计算目标季度的年和季度
        year = now.year
        current_quarter = (now.month - 1) // 3 + 1  # 1-3月是第1季度，4-6月是第2季度，以此类推
        target_quarter = current_quarter - quarters_ago
        
        # 处理季度溢出
        while target_quarter <= 0:
            year -= 1
            target_quarter += 4
        
        # 计算目标季度的第一个月
        first_month = (target_quarter - 1) * 3 + 1
        
        # 计算目标季度的开始时间（第一个月的1号）
        start_time = datetime(year, first_month, 1, tzinfo=self.tz)
        
        # 计算目标季度的最后一个月
        last_month = first_month + 2
        
        # 计算目标季度的结束时间（最后一个月的最后一天的23:59:59.999999）
        if last_month == 12:
            end_time = datetime(year + 1, 1, 1, tzinfo=self.tz) - timedelta(seconds=1)
        else:
            end_time = datetime(year, last_month + 1, 1, tzinfo=self.tz) - timedelta(seconds=1)
        
        end_time = end_time.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # 添加维度标识，传递给_collect_stats方法
        return self._collect_stats(start_time, end_time, dimension="quarterly")

    def _has_data(self, task_stats, material_stats, mix_project_stats, mix_artwork_stats=None):
        """检查是否有任何数据
        
        Args:
            task_stats: 任务统计数据
            material_stats: 素材统计数据
            mix_project_stats: 混剪项目统计数据
            mix_artwork_stats: 混剪成片统计数据
            
        Returns:
            bool: 是否有任何数据
        """
        # 如果没有任何统计数据，直接返回False
        if not task_stats and not material_stats and not mix_project_stats and not mix_artwork_stats:
            return False
            
        # 检查任务数据
        has_task_data = False
        if task_stats:
            try:
                has_task_data = any([
                    getattr(task_stats, 'total_tasks', 0) > 0,
                    getattr(task_stats, 'erase_tasks', 0) > 0,
                    getattr(task_stats, 'face_swap_tasks', 0) > 0,
                    getattr(task_stats, 'video_analysis_tasks', 0) > 0,
                    getattr(task_stats, 'text_to_image_tasks', 0) > 0,
                    getattr(task_stats, 'image_to_video_tasks', 0) > 0,
                    getattr(task_stats, 'script_creation_tasks', 0) > 0,
                    getattr(task_stats, 'ai_dubbing_tasks', 0) > 0,
                    getattr(task_stats, 'original_translation_tasks', 0) > 0,
                    getattr(task_stats, 'social_media_agent_tasks', 0) > 0,
                    getattr(task_stats, 'ad_agent_tasks', 0) > 0
                ])
            except Exception as e:
                logger.error(f"Error checking task_stats data: {str(e)}")
                has_task_data = False
            
        # 检查素材数据
        has_material_data = False
        if material_stats:
            try:
                has_material_data = getattr(material_stats, 'total_materials', 0) > 0
            except Exception as e:
                logger.error(f"Error checking material_stats data: {str(e)}")
                has_material_data = False
            
        # 检查混剪项目数据
        has_mix_project_data = False
        if mix_project_stats:
            try:
                has_mix_project_data = any([
                    getattr(mix_project_stats, 'total_mix_projects', 0) > 0
                ])
            except Exception as e:
                logger.error(f"Error checking mix_project_stats data: {str(e)}")
                has_mix_project_data = False
                
        # 检查混剪成片数据
        has_mix_artwork_data = False
        if mix_artwork_stats:
            try:
                has_mix_artwork_data = getattr(mix_artwork_stats, 'mix_project_videos', 0) > 0
            except Exception as e:
                logger.error(f"Error checking mix_artwork_stats data: {str(e)}")
                has_mix_artwork_data = False
            
        return has_task_data or has_material_data or has_mix_project_data or has_mix_artwork_data

    def _collect_stats(self, start_time, end_time, dimension="daily"):
        """收集指定时间范围的统计数据
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            dimension: 时间维度，默认为daily
            
        Returns:
            list: 统计数据列表
        """
        try:
            with get_db() as db:
                # 获取所有项目配置信息
                project_configs = db.execute(
                    text("SELECT project_id, project_name, team_group_info FROM project_config")
                ).fetchall()
                
                # 获取所有项目ID
                project_ids = [p.project_id for p in project_configs]
                
                # 批量查询任务数据
                task_stats = db.execute(
                    text("""
                        WITH task_stats AS (
                            SELECT 
                                project_id,
                                COUNT(*) as total_tasks,
                                SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as completed_tasks,
                                SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) as failed_tasks,
                                COUNT(CASE WHEN status = 'PROCESSING' THEN 1 END) as processing_tasks,
                                COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_tasks,
                                -- AI任务统计 - 只统计成功的任务
                                COUNT(CASE WHEN task_type = 'TASK_ERASE' AND status = 'SUCCESS' THEN 1 END) as erase_tasks,
                                COUNT(CASE WHEN (task_type = 'TASK_FACE_SWAP' OR task_type = 'TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' THEN 1 END) as face_swap_tasks,
                                COUNT(CASE WHEN (task_type = 'TASK_FACE_SWAP' OR task_type = 'TASK_MULTY_FACE_SWAP') AND status = 'SUCCESS' THEN 1 END) as face_swap_videos,
                                COUNT(CASE WHEN task_type = 'TASK_STORY_BOARD_ANA' AND status = 'SUCCESS' THEN 1 END) as video_analysis_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_MJ_TXT2IMG' AND status = 'SUCCESS' THEN 1 END) as text_to_image_tasks,
                                COUNT(CASE WHEN (task_type = 'TASK_IMG2VIDEO' OR task_type = 'TASK_IMG2VIDEO_MINIMAX' OR task_type = 'TASK_IMG2VIDEO_VIDU') AND status = 'SUCCESS' THEN 1 END) as image_to_video_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_AI_SCRIPT' AND status = 'SUCCESS' THEN 1 END) as script_creation_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_DUBBING' AND status = 'SUCCESS' THEN 1 END) as ai_dubbing_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_ASR' AND status = 'SUCCESS' THEN 1 END) as original_translation_tasks,
                                -- Agent任务统计
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH_V1' THEN 1 END) as social_media_agent_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH_V1' THEN 1 END) as social_media_agent_videos,
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH' THEN 1 END) as ad_agent_tasks,
                                COUNT(CASE WHEN task_type = 'TASK_AGENT_VOICE_MASH' THEN 1 END) as ad_agent_videos
                            FROM base_task bt
                            WHERE project_id = ANY(:project_ids)
                                AND create_time >= :start_time AT TIME ZONE 'UTC'
                                AND create_time <= :end_time AT TIME ZONE 'UTC'
                            GROUP BY project_id
                        )
                        SELECT * FROM task_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询素材数据
                material_stats = db.execute(
                    text("""
                        WITH material_stats AS (
                            SELECT 
                                project_id,
                                COUNT(*) as total_materials,
                                SUM(CASE WHEN deepspace_index_status = 'SUCCESS' THEN 1 ELSE 0 END) as processed_materials
                            FROM material
                            WHERE project_id = ANY(:project_ids)
                                AND created_time >= :start_time AT TIME ZONE 'UTC'
                                AND created_time <= :end_time AT TIME ZONE 'UTC'
                                AND delete_status = 'NORMAL'
                            GROUP BY project_id
                        )
                        SELECT * FROM material_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询混剪项目数据
                mix_project_stats = db.execute(
                    text("""
                        WITH mix_project_stats AS (
                            SELECT 
                                project_id,
                                COUNT(*) as total_mix_projects,
                                SUM(CASE WHEN delete_status = 'NORMAL' THEN 1 ELSE 0 END) as completed_mix_projects,
                                0 as mix_project_videos
                            FROM mix_project
                            WHERE project_id = ANY(:project_ids)
                                AND create_time >= :start_time AT TIME ZONE 'UTC'
                                AND create_time <= :end_time AT TIME ZONE 'UTC'
                                AND delete_status = 'NORMAL'
                            GROUP BY project_id
                        )
                        SELECT * FROM mix_project_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询混剪成片数据
                mix_artwork_stats = db.execute(
                    text("""
                        WITH mix_artwork_stats AS (
                            SELECT 
                                mp.project_id,
                                COUNT(ma.mix_artwork_id) as mix_project_videos
                            FROM mix_artwork ma
                            JOIN mix_project mp ON ma.mix_project_id = mp.mix_project_id
                            WHERE mp.project_id = ANY(:project_ids)
                                AND ma.artwork_vlc_id IS NOT NULL
                                AND mp.create_time >= :start_time AT TIME ZONE 'UTC'
                                AND mp.create_time <= :end_time AT TIME ZONE 'UTC'
                                AND mp.delete_status = 'NORMAL'
                            GROUP BY mp.project_id
                        )
                        SELECT * FROM mix_artwork_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 批量查询TTS配置数据（解说词配音）
                tts_stats = db.execute(
                    text("""
                        WITH tts_stats AS (
                            SELECT 
                                mp.project_id,
                                COUNT(DISTINCT CASE 
                                    WHEN sc.tts::text LIKE '%voice_id%' AND sc.tts::text LIKE '%tts_id%' 
                                    THEN s.shooting_id 
                                END) as tts_configs
                            FROM shooting s
                            JOIN mix_project mp ON s.mix_project_id = mp.mix_project_id
                            JOIN shooting_config sc ON s.shooting_config_id = sc.shooting_config_id
                            WHERE mp.project_id = ANY(:project_ids)
                                AND mp.create_time >= :start_time AT TIME ZONE 'UTC'
                                AND mp.create_time <= :end_time AT TIME ZONE 'UTC'
                                AND mp.delete_status = 'NORMAL'
                            GROUP BY mp.project_id
                        )
                        SELECT * FROM tts_stats
                    """),
                    {
                        "project_ids": project_ids,
                        "start_time": start_time,
                        "end_time": end_time
                    }
                ).fetchall()
                
                # 将查询结果转换为字典，方便查找
                task_stats_dict = {t.project_id: t for t in task_stats} if task_stats else {}
                material_stats_dict = {m.project_id: m for m in material_stats} if material_stats else {}
                mix_project_stats_dict = {m.project_id: m for m in mix_project_stats} if mix_project_stats else {}
                mix_artwork_stats_dict = {m.project_id: m for m in mix_artwork_stats} if mix_artwork_stats else {}
                tts_stats_dict = {t.project_id: t for t in tts_stats} if tts_stats else {}
                
                # 创建空统计对象，用于安全访问属性
                class EmptyStat:
                    def __getattr__(self, _):
                        return 0
                empty_stat = EmptyStat()
                
                # 按项目分组统计
                stats = []
                for project in project_configs:
                    project_id = project.project_id
                    project_name = project.project_name
                    team_info = {}
                    
                    # 安全地解析 team_group_info
                    if project.team_group_info:
                        try:
                            if isinstance(project.team_group_info, str):
                                team_info = json.loads(project.team_group_info)
                            else:
                                team_info = project.team_group_info
                        except Exception as e:
                            logger.error(f"Failed to parse team_group_info for project {project_id}: {str(e)}")
                            team_info = {}
                    
                    # 获取该项目的统计数据
                    task_stat = task_stats_dict.get(project_id)
                    material_stat = material_stats_dict.get(project_id)
                    mix_project_stat = mix_project_stats_dict.get(project_id)
                    tts_stat = tts_stats_dict.get(project_id)
                    
                    # 检查是否有数据
                    if not self._has_data(task_stat, material_stat, mix_project_stat, mix_artwork_stats_dict.get(project_id, empty_stat)):
                        continue
                    
                    # 安全获取部门和客户信息
                    department = '个人空间'
                    client = '未知客户'
                    if team_info and isinstance(team_info, dict):
                        team_detail = team_info.get('team_detail', {})
                        if isinstance(team_detail, dict):
                            department = team_detail.get('team_name', '个人空间')
                        
                        client_detail = team_info.get('client_detail', {})
                        if isinstance(client_detail, dict):
                            client = client_detail.get('client_name', '未知客户')
                    
                    # 创建统计记录
                    record = VideoStats(
                        start_time=start_time,
                        end_time=end_time,
                        department=department,
                        project=project_name or '未知项目',
                        client=client,
                        # 添加维度标识
                        dimension=dimension,
                        # AI任务统计 - 直接使用总数
                        erase_tasks=task_stat.erase_tasks if task_stat else 0,
                        face_swap_tasks=task_stat.face_swap_tasks if task_stat else 0,
                        face_swap_videos=task_stat.face_swap_videos if task_stat else 0,
                        video_analysis_tasks=task_stat.video_analysis_tasks if task_stat else 0,
                        text_to_image_tasks=task_stat.text_to_image_tasks if task_stat else 0,
                        image_to_video_tasks=task_stat.image_to_video_tasks if task_stat else 0,
                        script_creation_tasks=task_stat.script_creation_tasks if task_stat else 0,
                        # 其他任务统计
                        total_materials=material_stat.total_materials if material_stat else 0,
                        total_mix_projects=mix_project_stat.total_mix_projects if mix_project_stat else 0,
                        mix_project_videos=mix_artwork_stats_dict.get(project_id, empty_stat).mix_project_videos if project_id in mix_artwork_stats_dict else 0,
                        # AI配音和翻译
                        ai_dubbing_tasks=tts_stat.tts_configs if tts_stat else 0,
                        original_translation_tasks=task_stat.original_translation_tasks if task_stat else 0,
                        # Agent任务统计
                        social_media_agent_tasks=task_stat.social_media_agent_tasks if task_stat else 0,
                        social_media_agent_videos=task_stat.social_media_agent_videos if task_stat else 0,
                        ad_agent_tasks=task_stat.ad_agent_tasks if task_stat else 0,
                        ad_agent_videos=task_stat.ad_agent_videos if task_stat else 0
                    )
                    stats.append(record)
                
                return stats
        except Exception as e:
            # 打印traceback
            traceback.print_exc()
            logger.error(f"Failed to collect stats: {str(e)}")
            return []

    def update_feishu_tables(self, report_type=None):
        """更新飞书表格
        
        Args:
            report_type (str, optional): 报告类型，可选值为 "daily", "weekly", "monthly", "quarterly"。
                                        如果为None，则更新所有类型的报告。
        """
        try:
            if report_type is None or report_type == "daily":
                # 更新日数据
                daily_stats = self.collect_historical_daily_stats()
                if daily_stats:
                    self.feishu_service.update_table(daily_stats)
                    logger.info("Successfully updated daily Feishu table")

            if report_type is None or report_type == "weekly":
                # 更新周数据
                weekly_stats = self.collect_historical_weekly_stats()
                if weekly_stats:
                    self.feishu_service.update_table(weekly_stats)
                    logger.info("Successfully updated weekly Feishu table")

            if report_type is None or report_type == "monthly":
                # 更新月数据
                monthly_stats = self.collect_historical_monthly_stats()
                if monthly_stats:
                    self.feishu_service.update_table(monthly_stats)
                    logger.info("Successfully updated monthly Feishu table")

            if report_type is None or report_type == "quarterly":
                # 更新季度数据
                quarterly_stats = self.collect_historical_quarterly_stats()
                if quarterly_stats:
                    self.feishu_service.update_table(quarterly_stats)
                    logger.info("Successfully updated quarterly Feishu table")

            if report_type is None:
                logger.info("Successfully updated all Feishu tables")
            return True
        except Exception as e:
            logger.error(f"Failed to update Feishu tables: {str(e)}")
            return False 