import os
import json
import time
from typing import List, Dict, Any, Optional
from loguru import logger
import requests
from src.core.config import FEISHU_CONFIG
import datetime

class FeishuService:
    FIELD_MAPPING = {
        "start_time": "开始时间",
        "end_time": "结束时间",
        "department": "部门",
        "project": "项目",
        "client": "客户",
        "erase_tasks": "AI擦除任务数",
        "face_swap_tasks": "AI换脸任务数",
        "video_analysis_tasks": "AI视频解析任务数",
        "text_to_image_tasks": "AI文生图任务数",
        "image_to_video_tasks": "AI图生视频任务数",
        "script_creation_tasks": "创建脚本数",
        "total_materials": "素材数",
        "total_mix_projects": "混剪任务数",
        "mix_project_videos": "混剪任务-生成视频数",
        "ai_dubbing_tasks": "AI解说词配音-任务数",
        "original_translation_tasks": "原声翻译-任务数",
        "update_time": "更新时间"
    }
    # 反向映射，用于检查字段是否有效
    REVERSE_FIELD_MAPPING = {v: k for k, v in FIELD_MAPPING.items()}
    DATE_FIELDS = {"start_time", "end_time", "update_time"}  # 你表格中所有日期字段的原始字段名
    # 这里填写你表格中"多行文本"类型的所有列名（中文）
    MULTILINE_TEXT_FIELDS = {
        "项目", "客户", "部门"  # 举例，具体请根据你的表格设置填写
    }

    def __init__(self):
        """初始化飞书服务"""
        try:
            logger.info("初始化飞书服务...")
            self.app_id = FEISHU_CONFIG["app_id"]
            self.app_secret = FEISHU_CONFIG["app_secret"]
            self.app_token = FEISHU_CONFIG.get("app_token")
            self.access_token = None
            self.token_expire_time = 0
            
            if not self.app_token:
                logger.warning("未配置app_token，请检查配置文件或环境变量")
                raise ValueError("未配置app_token，无法初始化飞书服务")
            
            logger.debug(f"应用ID: {self.app_id}")
            logger.debug(f"应用密钥长度: {len(self.app_secret) if self.app_secret else 0}")
            logger.debug(f"应用Token: {self.app_token[:8]}...")
            logger.info("飞书服务初始化完成")
            
        except Exception as e:
            logger.error(f"飞书服务初始化失败: {str(e)}")
            raise

    def get_access_token(self) -> str:
        """获取飞书访问凭证"""
        try:
            # 检查token是否有效
            if self.access_token and self.token_expire_time > time.time() + 60:
                return self.access_token
                
            logger.info("开始获取飞书访问凭证...")
            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }
            headers = {
                "Content-Type": "application/json; charset=utf-8"
            }
            
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            result = response.json()
            
            if result.get("code") == 0:
                self.access_token = result.get("tenant_access_token")
                self.token_expire_time = time.time() + result.get("expire")
                logger.info("成功获取访问凭证")
                return self.access_token
            else:
                error_msg = f"获取token失败: {result.get('msg')}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
        except Exception as e:
            logger.error(f"获取访问凭证失败: {str(e)}")
            raise

    def _convert_record_to_dict(self, record: Any) -> Dict:
        """
        将记录对象转换为字典格式
        
        Args:
            record: 记录对象
            
        Returns:
            Dict: 转换后的字典
        """
        try:
            if isinstance(record, dict):
                result = record
            elif hasattr(record, '_sa_instance_state'):
                result = {}
                for key, value in record.__dict__.items():
                    if not key.startswith('_') and key != '_sa_instance_state':
                        try:
                            result[key] = self._serialize_value(value, key)
                        except Exception as e:
                            logger.error(f"字段 {key} 序列化失败: {str(e)}")
                            result[key] = None
                logger.debug(f"SQLAlchemy对象转换结果: {json.dumps(result, ensure_ascii=False)}")
            elif hasattr(record, '__dict__'):
                result = {}
                for key, value in record.__dict__.items():
                    if not key.startswith('_') and key != '_sa_instance_state':
                        try:
                            result[key] = self._serialize_value(value, key)
                        except Exception as e:
                            logger.error(f"字段 {key} 序列化失败: {str(e)}")
                            result[key] = None
                logger.debug(f"对象转换结果: {json.dumps(result, ensure_ascii=False)}")
            else:
                try:
                    result = dict(record)
                    for key, value in result.items():
                        try:
                            result[key] = self._serialize_value(value, key)
                        except Exception as e:
                            logger.error(f"字段 {key} 序列化失败: {str(e)}")
                            result[key] = None
                    logger.debug(f"可迭代对象转换结果: {json.dumps(result, ensure_ascii=False)}")
                except (TypeError, ValueError) as e:
                    logger.error(f"无法将记录转换为字典格式: {record}, 错误: {str(e)}")
                    raise ValueError(f"无法将记录转换为字典格式: {record}")
            # 字段名映射
            result = {self.FIELD_MAPPING.get(k, k): v for k, v in result.items()}
            return result
        except Exception as e:
            logger.error(f"记录转换失败: {str(e)}")
            raise

    def _serialize_value(self, value: Any, field_name: Optional[str] = None) -> Any:
        """
        序列化值，确保可以转换为JSON
        
        Args:
            value: 需要序列化的值
            field_name: 字段名（可选）
            
        Returns:
            序列化后的值
        """
        try:
            if value is None:
                return ""
                
            # 针对日期字段特殊处理 - 必须转为毫秒级时间戳
            if field_name in self.DATE_FIELDS or (isinstance(field_name, str) and field_name in self.REVERSE_FIELD_MAPPING and self.REVERSE_FIELD_MAPPING[field_name] in self.DATE_FIELDS):
                # 转换为毫秒级时间戳
                return self._to_timestamp(value)
                
            # 多行文本字段强制转字符串
            if field_name and self.FIELD_MAPPING.get(field_name, field_name) in self.MULTILINE_TEXT_FIELDS:
                return str(value)
            elif isinstance(value, (str, int, float, bool)):
                return value
            elif isinstance(value, (list, tuple)):
                return [self._serialize_value(item) for item in value]
            elif isinstance(value, dict):
                return {k: self._serialize_value(v) for k, v in value.items()}
            elif hasattr(value, 'strftime'):  # 处理datetime对象，非日期字段
                try:
                    # 移除时区信息
                    if hasattr(value, 'tzinfo') and value.tzinfo is not None:
                        value = value.replace(tzinfo=None)
                    # 非日期字段的datetime对象转为字符串
                    return value.strftime('%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    logger.error(f"datetime序列化失败: {str(e)}, 值: {value}")
                    return str(value)
            else:
                try:
                    return str(value)
                except Exception as e:
                    logger.error(f"值序列化失败: {str(e)}, 值类型: {type(value)}")
                    return ""
        except Exception as e:
            logger.error(f"值序列化失败: {str(e)}, 值类型: {type(value)}")
            return ""

    def _to_timestamp(self, date_value) -> int:
        """
        将日期值转换为毫秒级时间戳
        
        Args:
            date_value: 日期值（字符串、日期对象或时间戳）
            
        Returns:
            int: 毫秒级时间戳
        """
        try:
            if not date_value:
                # 默认返回当前时间的时间戳
                return int(datetime.datetime.now().timestamp() * 1000)
                
            if isinstance(date_value, (int, float)):
                # 如果已经是数字，检查是否已经是毫秒级时间戳
                if date_value > 1e12:  # 大约是2001年以后的毫秒级时间戳
                    return int(date_value)
                elif date_value > 1e9:  # 秒级时间戳
                    return int(date_value * 1000)
                
            # 尝试转换为日期对象
            dt = None
            if isinstance(date_value, str):
                try:
                    # 尝试解析字符串为日期
                    from dateutil import parser
                    dt = parser.parse(date_value)
                except Exception as e:
                    logger.debug(f"日期字符串解析尝试失败: {str(e)}, 尝试直接格式化")
                    # 尝试直接格式化
                    if '/' in date_value:
                        parts = date_value.split('/')
                        if len(parts) == 3:
                            year, month, day = parts
                            try:
                                dt = datetime.datetime(int(year), int(month), int(day))
                            except ValueError as e:
                                logger.error(f"日期格式化失败: {str(e)}")
                    elif '-' in date_value:
                        parts = date_value.split('-')
                        if len(parts) == 3:
                            year, month, day = parts
                            try:
                                dt = datetime.datetime(int(year), int(month), int(day))
                            except ValueError as e:
                                logger.error(f"日期格式化失败: {str(e)}")
            elif hasattr(date_value, 'timestamp'):  # datetime对象
                dt = date_value
                # 如果datetime对象带有时区，则先移除时区，以防止excel不支持带时区的时间
                if hasattr(dt, 'tzinfo') and dt.tzinfo is not None:
                    # 记录当前时区并移除
                    original_tz = dt.tzinfo
                    dt = dt.replace(tzinfo=None)
                    logger.debug(f"移除了时区信息 {original_tz} 从日期 {dt}")
            
            if dt:
                # 转换为毫秒级时间戳
                try:
                    timestamp = int(dt.timestamp() * 1000)
                    logger.debug(f"日期值 {date_value} 转换为时间戳: {timestamp}")
                    return timestamp
                except Exception as e:
                    logger.error(f"日期转时间戳失败: {str(e)}, 值: {date_value}")
            
            # 如果所有尝试都失败，返回当前时间戳
            logger.warning(f"无法将 {date_value} 转换为时间戳，使用当前时间代替")
            return int(datetime.datetime.now().timestamp() * 1000)
        except Exception as e:
            logger.error(f"转换日期为时间戳失败: {str(e)}")
            # 返回当前时间的时间戳作为后备
            return int(datetime.datetime.now().timestamp() * 1000)

    def _format_date(self, date_value) -> str:
        """
        格式化日期值为字符串格式
        
        Args:
            date_value: 日期值（字符串、日期对象或时间戳）
            
        Returns:
            str: 格式化后的日期字符串 YYYY-MM-DD
        """
        try:
            if not date_value:
                return ""
                
            if isinstance(date_value, str):
                try:
                    # 尝试解析字符串为日期
                    from dateutil import parser
                    dt = parser.parse(date_value)
                    return dt.strftime('%Y-%m-%d')
                except Exception as e:
                    logger.debug(f"日期字符串解析尝试失败: {str(e)}, 将尝试直接格式化")
                    # 尝试直接格式化
                    if '/' in date_value:
                        parts = date_value.split('/')
                        if len(parts) == 3:
                            year, month, day = parts
                            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    elif '-' in date_value:
                        parts = date_value.split('-')
                        if len(parts) == 3:
                            year, month, day = parts
                            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                    # 如果无法解析，返回原值
                    return date_value
            elif hasattr(date_value, 'strftime'):  # datetime对象
                return date_value.strftime('%Y-%m-%d')
            elif isinstance(date_value, (int, float)) and date_value > 0:  # 时间戳
                # 将秒级或毫秒级时间戳转为日期字符串
                try:
                    dt = datetime.datetime.fromtimestamp(date_value / 1000 if date_value > 1e10 else date_value)
                    return dt.strftime('%Y-%m-%d')
                except Exception as e:
                    logger.error(f"时间戳转日期失败: {str(e)}, 值: {date_value}")
                    return ""
            return str(date_value)
        except Exception as e:
            logger.error(f"格式化日期失败: {str(e)}")
            return ""

    def _normalize_date_fields(self, record: Dict) -> Dict:
        """
        规范化日期字段格式，转换为毫秒级时间戳
        
        Args:
            record: 记录字典
            
        Returns:
            规范化后的记录字典
        """
        try:
            # 处理中文字段名
            for field_name in ["开始时间", "结束时间", "更新时间"]:
                if field_name in record:
                    timestamp = self._to_timestamp(record[field_name])
                    record[field_name] = timestamp
                    logger.debug(f"日期字段 {field_name}: {record[field_name]} 转换为时间戳: {timestamp}")
            
            # 处理英文字段名
            for field_name in self.DATE_FIELDS:
                if field_name in record:
                    timestamp = self._to_timestamp(record[field_name])
                    record[field_name] = timestamp
                    logger.debug(f"日期字段 {field_name}: {record[field_name]} 转换为时间戳: {timestamp}")
            
            return record
        except Exception as e:
            logger.error(f"规范化日期字段失败: {str(e)}")
            return record

    def _filter_valid_fields(self, record: Dict) -> Dict:
        """
        过滤掉不在飞书表格中的字段
        
        Args:
            record: 原始记录字典
            
        Returns:
            Dict: 只包含有效字段的记录字典
        """
        valid_record = {}
        for key, value in record.items():
            # 检查字段是否在FIELD_MAPPING中或者是否是有效的中文字段名
            if key in self.FIELD_MAPPING.values() or key in self.REVERSE_FIELD_MAPPING:
                valid_record[key] = value
            else:
                logger.debug(f"过滤掉无效字段: {key}")
        return valid_record

    def batch_add_records(self, table_id: str, records: List[Dict]) -> Dict:
        """
        批量添加记录到飞书表格
        
        Args:
            table_id: 表格ID
            records: 记录列表，每个记录是一个字段字典
            
        Returns:
            添加结果
        """
        try:
            logger.info(f"开始批量添加记录到表格 {table_id}，记录数: {len(records)}")
            
            # 获取访问凭证
            access_token = self.get_access_token()
            
            # 确保所有记录都是字典格式并可以序列化
            converted_records = []
            for i, record in enumerate(records):
                try:
                    record_dict = self._convert_record_to_dict(record)
                    # 过滤不在飞书表格中的字段
                    record_dict = self._filter_valid_fields(record_dict)
                    # 添加当前时间作为更新时间（毫秒级时间戳）
                    record_dict["更新时间"] = int(datetime.datetime.now().timestamp() * 1000)
                    # 规范化日期字段格式
                    record_dict = self._normalize_date_fields(record_dict)
                    # 序列化所有值
                    serialized_record = {
                        key: self._serialize_value(value, key)
                        for key, value in record_dict.items()
                    }
                    converted_records.append(serialized_record)
                except Exception as e:
                    logger.error(f"处理第 {i+1} 条记录时失败: {str(e)}")
                    raise
            
            # 记录第一条记录的细节，方便调试
            if converted_records:
                first_record = converted_records[0]
                logger.debug(f"首条记录: {json.dumps(first_record, ensure_ascii=False)}")
                for key, value in first_record.items():
                    logger.debug(f"首条记录字段 {key}: {value} (类型: {type(value).__name__})")
            
            logger.debug(f"转换后的记录示例: {json.dumps(converted_records[0] if converted_records else {}, ensure_ascii=False)}")
            
            # 构建请求URL
            base_url = "https://open.feishu.cn/open-apis/bitable/v1/apps"
            url = f"{base_url}/{self.app_token}/tables/{table_id}/records/batch_create"
            
            # 构建请求体
            payload = {
                "records": [{"fields": record} for record in converted_records]
            }
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {json.dumps(payload, ensure_ascii=False)}")
            
            # 发起请求
            response = requests.post(url, headers=headers, data=json.dumps(payload))
            result = response.json()
            
            # 处理响应
            if result.get("code") == 0:
                logger.info(f"成功添加 {len(converted_records)} 条记录")
                return result.get("data", {})
            else:
                error_msg = f"批量添加记录失败: {result.get('msg')}, code: {result.get('code')}"
                logger.error(error_msg)
                # 记录更详细的错误信息
                if "errors" in result:
                    for err in result["errors"]:
                        logger.error(f"错误详情: {json.dumps(err, ensure_ascii=False)}")
                raise Exception(error_msg)
                
        except Exception as e:
            logger.error(f"批量添加记录失败: {str(e)}")
            raise

    def update_table(self, records: List[Dict], batch_size: int = 100, show_progress: bool = False) -> bool:
        """
        更新飞书表格数据
        
        Args:
            records: 要更新的记录列表
            batch_size: 每批处理的记录数
            show_progress: 是否显示进度条
            
        Returns:
            bool: 更新是否成功
        """
        try:
            logger.info(f"开始更新表格数据，总记录数: {len(records)}")
            
            # 按时间维度分组处理数据
            daily_records = []
            weekly_records = []
            monthly_records = []
            quarterly_records = []
            
            for record in records:
                try:
                    # 转换记录为字典格式
                    record_dict = self._convert_record_to_dict(record)
                    dimension = record_dict.get("dimension", "daily")
                    
                    if dimension == "daily":
                        daily_records.append(record_dict)
                    elif dimension == "weekly":
                        weekly_records.append(record_dict)
                    elif dimension == "monthly":
                        monthly_records.append(record_dict)
                    elif dimension == "quarterly":
                        quarterly_records.append(record_dict)
                except Exception as e:
                    logger.error(f"处理记录时失败: {str(e)}")
                    raise
            
            logger.info(f"数据分组完成: 日数据 {len(daily_records)} 条, 周数据 {len(weekly_records)} 条, "
                       f"月数据 {len(monthly_records)} 条, 季度数据 {len(quarterly_records)} 条")
            
            # 处理日数据
            if daily_records:
                logger.info(f"开始更新日数据，共 {len(daily_records)} 条记录")
                self._batch_process_records(
                    FEISHU_CONFIG["daily_stats_table_id"],
                    daily_records,
                    batch_size,
                    show_progress
                )
            
            # 处理周数据
            if weekly_records:
                logger.info(f"开始更新周数据，共 {len(weekly_records)} 条记录")
                self._batch_process_records(
                    FEISHU_CONFIG["weekly_stats_table_id"],
                    weekly_records,
                    batch_size,
                    show_progress
                )
            
            # 处理月数据
            if monthly_records:
                logger.info(f"开始更新月数据，共 {len(monthly_records)} 条记录")
                self._batch_process_records(
                    FEISHU_CONFIG["monthly_stats_table_id"],
                    monthly_records,
                    batch_size,
                    show_progress
                )
            
            # 处理季度数据
            if quarterly_records:
                logger.info(f"开始更新季度数据，共 {len(quarterly_records)} 条记录")
                self._batch_process_records(
                    FEISHU_CONFIG["quarterly_stats_table_id"],
                    quarterly_records,
                    batch_size,
                    show_progress
                )
            
            logger.info("所有数据更新完成")
            return True
            
        except Exception as e:
            logger.error(f"更新飞书表格失败: {str(e)}")
            return False
    
    def _batch_process_records(self, table_id: str, records: List[Dict], batch_size: int, show_progress: bool):
        """
        批量处理记录
        
        Args:
            table_id: 表格ID
            records: 记录列表
            batch_size: 每批处理的记录数
            show_progress: 是否显示进度条
        """
        try:
            total_records = len(records)
            processed = 0
            
            logger.info(f"开始批量处理记录，表格ID: {table_id}, 总记录数: {total_records}, 批处理大小: {batch_size}")
            
            while processed < total_records:
                batch = records[processed:processed + batch_size]
                try:
                    logger.debug(f"处理第 {processed+1} 到 {processed+len(batch)} 条记录")
                    result = self.batch_add_records(table_id, batch)
                    processed += len(batch)
                    if show_progress:
                        logger.info(f"已处理 {processed}/{total_records} 条记录")
                        
                except Exception as e:
                    logger.error(f"处理批次失败: {str(e)}")
                    raise
                    
            logger.info(f"批量处理完成，共处理 {processed} 条记录")
            
        except Exception as e:
            logger.error(f"批量处理记录失败: {str(e)}")
            raise

    def clear_table_data(self, table_id: str, batch_size: int = 100, show_progress: bool = False) -> bool:
        """
        清空表格中的所有数据
        
        Args:
            table_id: 表格ID
            batch_size: 每批处理的记录数量，默认100
            show_progress: 是否显示进度条
            
        Returns:
            bool: 清空操作是否成功
        """
        try:
            logger.info(f"开始清空表格 {table_id} 的所有数据...")
            
            # 获取访问凭证
            access_token = self.get_access_token()
            
            # 构建请求URL，获取所有记录
            base_url = "https://open.feishu.cn/open-apis/bitable/v1/apps"
            list_url = f"{base_url}/{self.app_token}/tables/{table_id}/records"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            # 获取所有记录ID
            all_record_ids = []
            page_token = None
            
            while True:
                params = {"page_size": 100}
                if page_token:
                    params["page_token"] = page_token
                
                response = requests.get(list_url, headers=headers, params=params)
                result = response.json()
                
                if result.get("code") != 0:
                    error_msg = f"获取记录失败: {result.get('msg')}, code: {result.get('code')}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                
                records = result.get("data", {}).get("items", [])
                record_ids = [record.get("record_id") for record in records]
                all_record_ids.extend(record_ids)
                
                # 检查是否有更多页
                page_token = result.get("data", {}).get("page_token")
                if not page_token:
                    break
            
            total_records = len(all_record_ids)
            logger.info(f"共获取到 {total_records} 条记录需要删除")
            
            if total_records == 0:
                logger.info("表格中没有数据，无需清空")
                return True
            
            # 批量删除记录
            delete_url = f"{base_url}/{self.app_token}/tables/{table_id}/records/batch_delete"
            processed = 0
            
            while processed < total_records:
                batch_ids = all_record_ids[processed:processed + batch_size]
                
                payload = {
                    "records": batch_ids
                }
                
                try:
                    response = requests.post(delete_url, headers=headers, data=json.dumps(payload))
                    result = response.json()
                    
                    if result.get("code") != 0:
                        error_msg = f"批量删除记录失败: {result.get('msg')}, code: {result.get('code')}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    
                    processed += len(batch_ids)
                    if show_progress:
                        logger.info(f"已删除 {processed}/{total_records} 条记录")
                    
                except Exception as e:
                    logger.error(f"删除批次失败: {str(e)}")
                    raise
            
            logger.info(f"表格 {table_id} 数据清空完成，共删除 {processed} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"清空表格数据失败: {str(e)}")
            return False

    def clear_all_tables_data(self, batch_size: int = 100, show_progress: bool = False, dimension: Optional[str] = None) -> bool:
        """
        清空所有表格或指定维度表格的数据
        
        Args:
            batch_size: 每批处理的记录数量，默认100
            show_progress: 是否显示进度条
            dimension: 指定要清空的维度表格，为None时清空所有维度表格
            
        Returns:
            bool: 清空操作是否全部成功
        """
        try:
            logger.info(f"开始清空{'所有' if dimension is None else dimension}表格数据...")
            success = True
            
            # 清空日表格
            if dimension is None or dimension == 'daily':
                logger.info("开始清空日数据表格...")
                table_id = FEISHU_CONFIG["daily_stats_table_id"]
                if not self.clear_table_data(table_id, batch_size, show_progress):
                    success = False
            
            # 清空周表格
            if dimension is None or dimension == 'weekly':
                logger.info("开始清空周数据表格...")
                table_id = FEISHU_CONFIG["weekly_stats_table_id"]
                if not self.clear_table_data(table_id, batch_size, show_progress):
                    success = False
            
            # 清空月表格
            if dimension is None or dimension == 'monthly':
                logger.info("开始清空月数据表格...")
                table_id = FEISHU_CONFIG["monthly_stats_table_id"]
                if not self.clear_table_data(table_id, batch_size, show_progress):
                    success = False
            
            # 清空季度表格
            if dimension is None or dimension == 'quarterly':
                logger.info("开始清空季度数据表格...")
                table_id = FEISHU_CONFIG["quarterly_stats_table_id"]
                if not self.clear_table_data(table_id, batch_size, show_progress):
                    success = False
            
            if success:
                logger.info("所有表格数据清空完成")
            else:
                logger.warning("部分表格数据清空失败")
                
            return success
            
        except Exception as e:
            logger.error(f"清空表格数据失败: {str(e)}")
            return False
