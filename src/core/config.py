import os
from dotenv import load_dotenv
from pathlib import Path
import configparser
from typing import Dict, Any, List, Optional, Union, Tuple
from loguru import logger
import json

# 加载环境变量
load_dotenv()

# 项目根目录
ROOT_DIR = Path(__file__).parent.parent.parent

# 日志配置
LOG_DIR = ROOT_DIR / "logs"
LOG_FILE = LOG_DIR / "app.log"

# 读取配置文件
config = configparser.ConfigParser()
config.read(ROOT_DIR / "conf" / "config.conf")

# 数据库配置
DB_CONFIG = {
    "host": config.get("database", "host", fallback="localhost"),
    "port": config.getint("database", "port", fallback=5432),
    "database": config.get("database", "database", fallback="video_producer"),
    "user": config.get("database", "user", fallback="video_producer"),
    "password": config.get("database", "password", fallback=""),
    "charset": config.get("database", "charset", fallback="utf8mb4")
}

# 飞书配置
FEISHU_CONFIG = {
    # 优先使用环境变量，否则从配置文件读取
    "app_id": os.getenv("FEISHU_APP_ID", config.get("feishu", "app_id", fallback="")),
    "app_secret": os.getenv("FEISHU_APP_SECRET", config.get("feishu", "app_secret", fallback="")),
    
    # 表格配置
    "daily_stats_table_id": config.get("feishu", "daily_stats_table_id", fallback=""),
    "daily_stats_view_id": config.get("feishu", "daily_stats_view_id", fallback=""),
    "weekly_stats_table_id": config.get("feishu", "weekly_stats_table_id", fallback=""),
    "weekly_stats_view_id": config.get("feishu", "weekly_stats_view_id", fallback=""),
    "monthly_stats_table_id": config.get("feishu", "monthly_stats_table_id", fallback=""),
    "monthly_stats_view_id": config.get("feishu", "monthly_stats_view_id", fallback=""),
    "quarterly_stats_table_id": config.get("feishu", "quarterly_stats_table_id", fallback=""),
    "quarterly_stats_view_id": config.get("feishu", "quarterly_stats_view_id", fallback=""),
    
    # 兼容单表格配置
    "app_token": os.getenv("FEISHU_APP_TOKEN", config.get("feishu", "app_token", fallback="")),
    "table_id": os.getenv("FEISHU_TABLE_ID", "")
}

# 任务配置
TASK_CONFIG = {
    "update_interval": int(os.getenv("UPDATE_INTERVAL", "3600")),  # 默认每小时更新一次
    "batch_size": int(os.getenv("BATCH_SIZE", "1000"))
}

# 确保日志目录存在
LOG_DIR.mkdir(exist_ok=True)

# 在配置加载完成后打印关键配置信息（用于调试）
if config.get("PROJECT", "ENV", fallback="development") == "development":
    logger.debug(f"项目环境: {config.get('PROJECT', 'ENV', fallback='development')}")
    logger.debug(f"数据库配置: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
    
# 导出函数，获取各区域配置
def get_db_config() -> Dict[str, Any]:
    return DB_CONFIG

def get_feishu_config() -> Dict[str, Any]:
    return FEISHU_CONFIG

def get_log_config() -> Dict[str, Any]:
    return {
        "level": config.get("LOG", "LEVEL", fallback="INFO"),
        "format": config.get("LOG", "FORMAT", fallback="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}"),
        "rotation": config.get("LOG", "ROTATION", fallback="1 week"),
        "retention": config.get("LOG", "RETENTION", fallback="1 month"),
        "compression": config.get("LOG", "COMPRESSION", fallback="zip")
    } 