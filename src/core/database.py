from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager
from .config import DB_CONFIG
import time
from loguru import logger

# 创建数据库引擎，增加连接池配置
DATABASE_URL = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,  # 自动检测连接是否有效
    pool_recycle=3600,   # 一小时后回收连接
    pool_size=10,        # 连接池大小
    max_overflow=20,     # 允许的最大连接数量
    pool_timeout=30      # 等待连接的超时时间
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 连接重试次数和等待时间
MAX_RETRIES = 3
RETRY_DELAY = 5  # 秒

@contextmanager
def get_db():
    """数据库会话上下文管理器，带连接重试机制"""
    retries = 0
    last_error = None
    
    while retries < MAX_RETRIES:
        try:
            db = SessionLocal()
            # 测试连接是否有效
            db.execute(text("SELECT 1"))
            break
        except Exception as e:
            last_error = e
            logger.warning(f"数据库连接失败 (尝试 {retries+1}/{MAX_RETRIES}): {str(e)}")
            if db:
                try:
                    db.close()
                except:
                    pass
            retries += 1
            if retries < MAX_RETRIES:
                logger.info(f"等待 {RETRY_DELAY} 秒后重试...")
                time.sleep(RETRY_DELAY)
    
    if retries == MAX_RETRIES:
        logger.error(f"数据库连接失败，已达到最大重试次数: {last_error}")
        raise last_error
    
    try:
        yield db
    finally:
        db.close() 