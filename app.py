import os
import sys
import time
import signal
import schedule
from datetime import datetime, timedelta
from loguru import logger
from sqlalchemy import text
from src.core.config import LOG_FILE, TASK_CONFIG
from src.core.database import get_db
from src.services.feishu_service import FeishuService
from src.services.stats_service import StatsService

# 配置日志
logger.add(LOG_FILE, rotation="500 MB", retention="10 days")

# 全局变量，用于控制程序是否继续运行
RUNNING = True

class StatsApp:
    def __init__(self):
        self.running = False
        self.feishu_service = FeishuService()
        self.stats_service = StatsService()
        self.stats_service.feishu_service = self.feishu_service
        # 设置飞书推送的批处理大小为500
        self.batch_size = 500

    def export_stats_to_feishu(self, dimension=None):
        """将统计数据导出到飞书互动表格
        
        Args:
            dimension: 指定导出的时间维度，为None时导出所有维度
            
        Returns:
            bool: 导出是否成功
        """
        try:
            # 首先验证数据库连接
            if not self.check_db_connection():
                logger.error("数据库连接无效，无法导出数据")
                return False
                
            all_stats = []
            
            # 在每个关键点检查是否应该继续运行
            if not RUNNING:
                logger.info("收到终止信号，停止数据收集")
                return False
                
            # 根据指定的维度收集数据
            if not dimension or dimension == 'daily':
                # 收集历史日统计数据
                logger.info("收集历史日统计数据...")
                daily_stats = self.stats_service.collect_historical_daily_stats()
                logger.info(f"收集到 {len(daily_stats)} 条日统计数据")
                all_stats.extend(daily_stats)
            
            if not RUNNING:
                logger.info("收到终止信号，停止数据收集")
                return False
                
            if not dimension or dimension == 'weekly':
                # 收集历史周统计数据
                logger.info("收集历史周统计数据...")
                weekly_stats = self.stats_service.collect_historical_weekly_stats()
                logger.info(f"收集到 {len(weekly_stats)} 条周统计数据")
                all_stats.extend(weekly_stats)
            
            if not RUNNING:
                logger.info("收到终止信号，停止数据收集")
                return False
                
            if not dimension or dimension == 'monthly':
                # 收集历史月统计数据
                logger.info("收集历史月统计数据...")
                monthly_stats = self.stats_service.collect_historical_monthly_stats()
                logger.info(f"收集到 {len(monthly_stats)} 条月统计数据")
                all_stats.extend(monthly_stats)
            
            if not RUNNING:
                logger.info("收到终止信号，停止数据收集")
                return False
                
            if not dimension or dimension == 'quarterly':
                # 收集历史季度统计数据
                logger.info("收集历史季度统计数据...")
                quarterly_stats = self.stats_service.collect_historical_quarterly_stats()
                logger.info(f"收集到 {len(quarterly_stats)} 条季度统计数据")
                all_stats.extend(quarterly_stats)
            
            if not RUNNING:
                logger.info("收到终止信号，停止数据收集")
                return False
                
            logger.info(f"总计收集到 {len(all_stats)} 条统计数据")
            
            # 先清空飞书表格中的数据
            logger.info("开始清空飞书表格中的现有数据...")
            clear_success = self.feishu_service.clear_all_tables_data(
                batch_size=self.batch_size,
                show_progress=True,
                dimension=dimension
            )
            
            if not RUNNING:
                logger.info("收到终止信号，停止数据导出")
                return False
                
            if not clear_success:
                logger.warning("清空飞书表格数据失败或部分失败，继续尝试导出数据...")
            
            # 导出数据到飞书
            logger.info("开始导出数据到飞书互动表格...")
            success = self.feishu_service.update_table(
                records=all_stats,
                batch_size=self.batch_size,
                show_progress=True
            )
            
            return success
            
        except Exception as e:
            logger.error(f"导出过程中发生错误: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def check_db_connection(self):
        """验证数据库连接是否有效，如果无效则尝试重新连接"""
        try:
            with get_db() as db:
                db.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {str(e)}")
            return False

    def update_daily_report(self):
        """更新日报"""
        if not RUNNING:
            return
        # 先检查数据库连接
        if not self.check_db_connection():
            logger.error("数据库连接无效，无法更新日报")
            return
        logger.info("Running daily report update")
        self.export_stats_to_feishu(dimension="daily")

    def update_weekly_report(self):
        """更新周报"""
        if not RUNNING:
            return
        # 先检查数据库连接
        if not self.check_db_connection():
            logger.error("数据库连接无效，无法更新周报")
            return
        logger.info("Running weekly report update")
        self.export_stats_to_feishu(dimension="weekly")

    def update_monthly_report(self):
        """更新月报"""
        if not RUNNING:
            return
        # 先检查数据库连接
        if not self.check_db_connection():
            logger.error("数据库连接无效，无法更新月报")
            return
        logger.info("Running monthly report update")
        self.export_stats_to_feishu(dimension="monthly")

    def update_quarterly_report(self):
        """更新季度报"""
        if not RUNNING:
            return
        # 先检查数据库连接
        if not self.check_db_connection():
            logger.error("数据库连接无效，无法更新季度报")
            return
        logger.info("Running quarterly report update")
        self.export_stats_to_feishu(dimension="quarterly")

    def update_all_reports(self):
        """更新所有报告"""
        if not RUNNING:
            return
        # 先检查数据库连接
        if not self.check_db_connection():
            logger.error("数据库连接无效，无法更新所有报告")
            return
        logger.info("Running all reports update")
        self.export_stats_to_feishu()

    def start(self):
        """启动应用"""
        try:
            # 验证数据库连接
            try:
                with get_db() as db:
                    db.execute(text("SELECT 1"))
                    logger.info("数据库连接成功")
            except Exception as e:
                logger.error(f"数据库连接失败: {str(e)}")
                sys.exit(1)
            
            # 设置定时任务 - 每天晚上11点运行所有报告更新
            schedule.every().day.at("23:00").do(self.update_all_reports)
            
            # 如果配置中指定了更新间隔，也保留原来的定时任务
            if "update_interval" in TASK_CONFIG:
                schedule.every(TASK_CONFIG["update_interval"]).seconds.do(
                    self.update_all_reports
                )
            
            # 判断是否需要立即执行一次
            if len(sys.argv) > 1 and sys.argv[1] == "--run-once":
                logger.info("Running one-time update")
                self.update_all_reports()
                return
            
            self.running = True
            logger.info("Application started successfully")
            
            # 运行定时任务
            while self.running and RUNNING:
                schedule.run_pending()
                time.sleep(1)
                
        except Exception as e:
            logger.error(f"Application failed to start: {str(e)}")
            sys.exit(1)

    def stop(self):
        """停止应用"""
        global RUNNING
        RUNNING = False
        self.running = False
        logger.info("Application stopped")

def main():
    # 设置全局运行状态
    global RUNNING
    RUNNING = True
    
    app = StatsApp()
    
    def signal_handler(signum, frame):
        """信号处理函数，确保程序能够被Ctrl+C中断"""
        global RUNNING
        logger.info(f"Received signal {signum}, shutting down...")
        RUNNING = False
        app.stop()
        # 强制退出程序
        logger.info("Force exiting program")
        os._exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动应用
    app.start()

if __name__ == "__main__":
    main()
