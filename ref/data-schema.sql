create table if not exists public.agent_project
(
    agent_project_id         bigserial
        primary key,
    created_time             timestamp,
    updated_time             timestamp,
    delete_status            deletestatus,
    user_uid                 bigint,
    project_id               varchar,
    agent_name               varchar,
    agent_type               varchar,
    ratio                    varchar,
    resolution               varchar,
    duration                 varchar,
    material_info_list       json,
    shooting_head            json,
    shooting_tail            json,
    script_config            json,
    theme_config             json,
    mix_project_sound_effect json,
    mix_project_sticker      json,
    captions                 json,
    clip_source              varchar default 'deepspace'::character varying
);

comment on column public.agent_project.agent_project_id is '代理项目ID';

comment on column public.agent_project.created_time is '创建时间';

comment on column public.agent_project.updated_time is '更新时间';

comment on column public.agent_project.delete_status is '删除状态';

comment on column public.agent_project.user_uid is '用户ID';

comment on column public.agent_project.project_id is '项目ID';

comment on column public.agent_project.agent_name is '名称';

comment on column public.agent_project.agent_type is '代理类型';

comment on column public.agent_project.ratio is '画面比例(自定义为: custom)';

comment on column public.agent_project.resolution is '分辨率';

comment on column public.agent_project.duration is '时长';

comment on column public.agent_project.material_info_list is '素材信息列表';

comment on column public.agent_project.shooting_head is '片头';

comment on column public.agent_project.shooting_tail is '片尾';

comment on column public.agent_project.script_config is '脚本配置';

comment on column public.agent_project.theme_config is '主题配置';

comment on column public.agent_project.mix_project_sound_effect is '混剪工程背景音乐配置';

comment on column public.agent_project.mix_project_sticker is '混剪工程贴纸配置';

comment on column public.agent_project.captions is '分镜字幕配置';

alter table public.agent_project
    owner to ai_video_producer_pro;

create table if not exists public.agent_prompt
(
    agent_prompt_id   serial
        primary key,
    created_time      timestamp,
    updated_time      timestamp,
    delete_status     deletestatus,
    agent_prompt_name varchar,
    content           varchar,
    description       varchar
);

comment on column public.agent_prompt.delete_status is '删除状态';

alter table public.agent_prompt
    owner to ai_video_producer_pro;

create table if not exists public.agent_step
(
    step_id      serial
        primary key,
    created_time timestamp,
    updated_time timestamp,
    step_name    varchar,
    task_id      integer,
    sub_task_id  integer,
    input_params json,
    result       json
);

alter table public.agent_step
    owner to ai_video_producer_pro;

create table if not exists public.base_task
(
    task_id            bigserial
        primary key,
    task_type          varchar(255)       not null,
    creator            bigint,
    name               varchar(1024),
    status             varchar(255)       not null,
    create_time        timestamp          not null,
    start_time         timestamp,
    end_time           timestamp,
    error_times        integer  default 0 not null,
    subtask_id         bigint             not null,
    last_update        timestamp,
    err                text,
    dependence_ids     json,
    waiting_dependence smallint default 0 not null,
    is_canceled        boolean  default false,
    project_id         varchar(255),
    mix_project_id     bigint,
    trigger            varchar(64),
    parent_task_id     bigint,
    constraint unique_subtask
        unique (task_type, subtask_id)
);

comment on column public.base_task.task_id is '主键';

comment on column public.base_task.task_type is '任务类型（脚本解析， 压制成片， 素材换脸。。。。）';

comment on column public.base_task.creator is '创建者id';

comment on column public.base_task.name is '任务名称';

comment on column public.base_task.status is '任务状态';

comment on column public.base_task.create_time is '创建时间';

comment on column public.base_task.start_time is '开始执行时间';

comment on column public.base_task.end_time is '执行结束时间';

comment on column public.base_task.error_times is '重试次数';

comment on column public.base_task.subtask_id is '关联的子任务的id';

comment on column public.base_task.err is '消费级别的错误信息（比如未找到任务id）';

comment on column public.base_task.dependence_ids is '依赖的任务列表， json格式的list';

comment on column public.base_task.waiting_dependence is '1表示等待依赖，0表示无需等待，可以执行';

comment on column public.base_task.parent_task_id is '表示由哪个task衍生创建出来的任务';

alter table public.base_task
    owner to ai_video_producer_pro;

create index if not exists status_query
    on public.base_task using hash (status);

create index if not exists task_type_query
    on public.base_task using hash (task_type);

create table if not exists public.base_task_message_box
(
    task_id     bigint not null,
    creator     bigint not null,
    is_read     boolean default false,
    create_time timestamp(6),
    primary key (task_id, creator)
);

alter table public.base_task_message_box
    owner to ai_video_producer_pro;

create table if not exists public.custom_config
(
    conf_id       bigint       default nextval('custom_config_config_id_seq'::regclass) not null
        primary key,
    custom_type   varchar(64),
    key           varchar(64),
    value         text,
    create_time   timestamp(6) default CURRENT_TIMESTAMP,
    last_update   timestamp(6) default CURRENT_TIMESTAMP,
    delete_status deletestatus default 'NORMAL'::deletestatus,
    is_default    boolean      default false
);

alter table public.custom_config
    owner to ai_video_producer_pro;

create table if not exists public.dir
(
    id             bigserial
        primary key,
    name           varchar(255),
    parent_id      bigint default 0,
    create_time    timestamp with time zone,
    project_id     varchar(255),
    mix_project_id bigint
);

alter table public.dir
    owner to ai_video_producer_pro;

create table if not exists public.event
(
    event_id       bigserial
        primary key,
    event_name     varchar(256),
    event_desc     varchar(512),
    event_type     event_type,
    operation_type operation_type,
    event_detail   json,
    event_src      varchar(1024),
    user_uid       bigint,
    project_id     varchar(64),
    mix_project_id bigint,
    event_time     timestamp
);

alter table public.event
    owner to ai_video_producer_pro;

create table if not exists public.face_analyse_result
(
    face_result_analyse_id bigserial
        primary key,
    created_time           timestamp,
    updated_time           timestamp,
    delete_status          deletestatus,
    face_info              json,
    face_swap_project_id   bigint,
    face_swap_group_id     bigint,
    face_swap_group_name   varchar,
    data_source            varchar default 'analyse'::character varying
);

comment on column public.face_analyse_result.face_result_analyse_id is '换脸解析ID';

comment on column public.face_analyse_result.created_time is '创建时间';

comment on column public.face_analyse_result.updated_time is '更新时间';

comment on column public.face_analyse_result.delete_status is '删除状态';

comment on column public.face_analyse_result.face_info is '人脸信息';

comment on column public.face_analyse_result.face_swap_project_id is '换脸工程ID';

comment on column public.face_analyse_result.face_swap_group_id is '换脸组ID';

comment on column public.face_analyse_result.face_swap_group_name is '换脸组名称';

comment on column public.face_analyse_result.data_source is '来源';

alter table public.face_analyse_result
    owner to ai_video_producer_pro;

create table if not exists public.face_swap_group
(
    face_swap_group_id    bigserial
        primary key,
    created_time          timestamp,
    updated_time          timestamp,
    delete_status         deletestatus,
    face_swap_project_id  bigint,
    name                  varchar,
    target_swap_face_info json
);

comment on column public.face_swap_group.face_swap_group_id is '换脸组ID';

comment on column public.face_swap_group.created_time is '创建时间';

comment on column public.face_swap_group.updated_time is '更新时间';

comment on column public.face_swap_group.delete_status is '删除状态';

comment on column public.face_swap_group.face_swap_project_id is '换脸工程ID';

comment on column public.face_swap_group.name is '换脸组名称';

comment on column public.face_swap_group.target_swap_face_info is '目标人脸信息';

alter table public.face_swap_group
    owner to ai_video_producer_pro;

create index if not exists ix_face_swap_group_face_swap_group_id
    on public.face_swap_group (face_swap_group_id);

create index if not exists ix_face_swap_group_face_swap_project_id
    on public.face_swap_group (face_swap_project_id);

create table if not exists public.face_swap_project
(
    face_swap_project_id  bigserial
        primary key,
    created_time          timestamp,
    updated_time          timestamp,
    delete_status         deletestatus,
    project_id            varchar,
    creator_uid           integer,
    data_source           varchar,
    source_id_list        bigint[],
    target_swap_face_info json,
    ai_analyse_task_id    bigint,
    ai_analyse_face_info  json,
    source_vlc_id_map     json
);

comment on column public.face_swap_project.face_swap_project_id is '换脸工程ID';

comment on column public.face_swap_project.created_time is '创建时间';

comment on column public.face_swap_project.updated_time is '更新时间';

comment on column public.face_swap_project.delete_status is '删除状态';

comment on column public.face_swap_project.project_id is '项目ID';

comment on column public.face_swap_project.creator_uid is '创建者UID';

comment on column public.face_swap_project.data_source is '来源 (material, mix_artwork)';

comment on column public.face_swap_project.source_id_list is '来源表ID列表';

comment on column public.face_swap_project.target_swap_face_info is '替换后的人脸信息(目标人脸信息)';

comment on column public.face_swap_project.ai_analyse_task_id is 'AI解析任务ID';

comment on column public.face_swap_project.ai_analyse_face_info is 'AI解析后的视频人脸信息';

alter table public.face_swap_project
    owner to ai_video_producer_pro;

create table if not exists public.language
(
    language_id   serial
        primary key,
    created_time  timestamp,
    updated_time  timestamp,
    delete_status deletestatus,
    lang_code     varchar,
    lang_name     varchar,
    lang_desc     varchar
);

comment on column public.language.language_id is '语言id';

comment on column public.language.created_time is '创建时间';

comment on column public.language.updated_time is '更新时间';

comment on column public.language.delete_status is '删除状态';

comment on column public.language.lang_code is '语言代码';

comment on column public.language.lang_name is '语言名称';

comment on column public.language.lang_desc is '语言描述';

alter table public.language
    owner to ai_video_producer_pro;

create table if not exists public.material
(
    material_id            bigserial
        primary key,
    created_time           timestamp,
    updated_time           timestamp,
    delete_status          deletestatus,
    material_name          varchar,
    material_source        enummaterialsource,
    material_type          varchar,
    creator_uid            integer,
    vlc_id                 varchar,
    source_info            json,
    project_id             varchar,
    mix_project_id         integer,
    upload_record_id       bigint,
    deepspace_index_status enumdeepspaceindexstatus,
    asr_deepspace          json,
    asr_blueai             json,
    material_uuid          uuid default uuid_generate_v4() not null,
    parent_id              bigint,
    root_id                bigint,
    tag_list               character varying[],
    scene_deepspace        json,
    plot_deepspace         json,
    agent_deepspace        json,
    baidu_index_status     enumdeepspaceindexstatus
);

comment on column public.material.material_id is '素材ID';

comment on column public.material.created_time is '创建时间';

comment on column public.material.updated_time is '更新时间';

comment on column public.material.delete_status is '删除状态';

comment on column public.material.material_name is '素材名称';

comment on column public.material.material_source is '素材来源';

comment on column public.material.material_type is '素材类型';

comment on column public.material.creator_uid is '创建者UID';

comment on column public.material.vlc_id is 'VLC(volcanic cloud)(火山云源素材ID)';

comment on column public.material.source_info is '火山云源素材信息';

comment on column public.material.project_id is '项目ID';

comment on column public.material.mix_project_id is '混剪工程ID';

comment on column public.material.upload_record_id is '上传记录ID';

comment on column public.material.deepspace_index_status is '深空索引状态';

comment on column public.material.asr_deepspace is '素材由深空解析的asr内容';

comment on column public.material.asr_blueai is '素材由自持服务解析的asr内容';

comment on column public.material.material_uuid is 'uuid';

alter table public.material
    owner to ai_video_producer_pro;

create index if not exists ix_material_material_id
    on public.material (material_id);

create index if not exists ix_material_vlc_id
    on public.material (vlc_id);

create index if not exists project_id_index
    on public.material (project_id);

create table if not exists public.material_share_record
(
    material_share_record_id bigserial
        primary key,
    created_time             timestamp,
    updated_time             timestamp,
    delete_status            deletestatus,
    material_id_list         bigint[],
    creator                  bigint,
    record_uuid              varchar(36)
);

comment on column public.material_share_record.material_share_record_id is '素材分享记录ID';

comment on column public.material_share_record.created_time is '创建时间';

comment on column public.material_share_record.updated_time is '更新时间';

comment on column public.material_share_record.delete_status is '删除状态';

comment on column public.material_share_record.material_id_list is '素材ID列表';

comment on column public.material_share_record.creator is '分享者';

comment on column public.material_share_record.record_uuid is '分享记录UUID';

alter table public.material_share_record
    owner to ai_video_producer_pro;

create index if not exists ix_material_share_record_material_share_record_id
    on public.material_share_record (material_share_record_id);

create unique index if not exists ix_material_share_record_record_uuid
    on public.material_share_record (record_uuid);

create table if not exists public.merchant
(
    name         varchar(255),
    merchant_uid varchar(64) not null
        primary key,
    create_time  timestamp
);

alter table public.merchant
    owner to ai_video_producer_pro;

create table if not exists public.mix_artwork
(
    mix_artwork_id      bigserial
        primary key,
    mix_project_id      bigint,
    source_candidate_id bigint,
    source_task_id      bigint,
    artwork_store_uri   varchar(1024),
    artwork_vlc_id      varchar(255),
    parent_id           bigint,
    tag_list            character varying[],
    artwork_source_info json,
    material_id         bigint,
    subtitle            text,
    subtitle_dubbing    text,
    name                varchar(255),
    dir_id              integer default 0     not null,
    choice              boolean default false not null
);

comment on column public.mix_artwork.artwork_source_info is '成片源信息';

comment on column public.mix_artwork.material_id is '关联素材ID';

alter table public.mix_artwork
    owner to ai_video_producer_pro;

create table if not exists public.mix_artwork_candidate
(
    candidate_id                bigserial
        primary key,
    mix_project_id              bigint,
    repetition_degree           real,
    duration                    real,
    candidate_config            json,
    artwork_id                  bigint,
    artwork_source_task_id      bigint,
    create_time                 timestamp,
    last_update                 timestamp,
    delete_status               deletestatus default 'NORMAL'::deletestatus,
    is_ai_recommend             boolean      default false,
    src_mix_project_last_modify timestamp,
    group_uid                   varchar(64),
    creator                     bigint
);

comment on table public.mix_artwork_candidate is '混剪备片';

alter table public.mix_artwork_candidate
    owner to ai_video_producer_pro;

create table if not exists public.mix_project
(
    mix_project_id          bigserial
        primary key,
    name                    varchar(255),
    creator                 bigint,
    story_board_id          bigint,
    config_id               bigint,
    create_time             timestamp,
    last_update             timestamp,
    delete_status           deletestatus default 'NORMAL'::deletestatus,
    project_id              varchar(255),
    mix_project_last_modify timestamp,
    source                  varchar      default 'default'::character varying,
    agent_project_id        bigint
);

comment on column public.mix_project.source is '来源';

comment on column public.mix_project.agent_project_id is 'Agent项目ID';

alter table public.mix_project
    owner to ai_video_producer_pro;

create table if not exists public.mix_project_config
(
    mix_project_config_id bigint       default nextval('mix_project_id_config_mix_project_id_config_id_seq'::regclass) not null
        constraint mix_project_id_config_pkey
            primary key,
    sound_effect          json,
    sticker               json,
    ratio                 varchar(64),
    resolution            varchar(64),
    digi_man_config       json,
    create_time           timestamp,
    last_update           timestamp,
    fps                   integer,
    delete_status         deletestatus default 'NORMAL'::deletestatus,
    cover                 json,
    mix_project_id        integer,
    filter                json,
    position_config       json
);

comment on column public.mix_project_config.mix_project_id is '混剪项目ID';

comment on column public.mix_project_config.filter is '滤镜配置';

alter table public.mix_project_config
    owner to ai_video_producer_pro;

create table if not exists public.project_config
(
    project_id          varchar(64) not null
        primary key,
    keywords_list       json,
    project_name        varchar,
    keywords_scene_list json,
    team_group_info     json
);

comment on table public.project_config is '视频项目配置';

alter table public.project_config
    owner to ai_video_producer_pro;

create table if not exists public.rel_project_deepspace_retrive_target
(
    id            bigserial
        primary key,
    type          varchar(128) default 'SKU'::character varying,
    merchant_uid  varchar(128),
    sku_uid       varchar(128),
    project_id    varchar(128),
    delete_status deletestatus default 'NORMAL'::deletestatus,
    is_pushed     boolean      default false not null
);

comment on table public.rel_project_deepspace_retrive_target is '项目与深空检索对象绑定关系';

alter table public.rel_project_deepspace_retrive_target
    owner to ai_video_producer_pro;

create table if not exists public.rel_user_muse
(
    user_uid      bigint not null,
    muse_user     bigint not null,
    name          varchar(128),
    delete_status deletestatus default 'NORMAL'::deletestatus,
    primary key (user_uid, muse_user)
);

alter table public.rel_user_muse
    owner to ai_video_producer_pro;

create table if not exists public.share_record
(
    share_record_id bigserial
        primary key,
    created_time    timestamp,
    updated_time    timestamp,
    delete_status   deletestatus,
    share_source    varchar,
    source_id_list  bigint[],
    creator         bigint,
    record_uuid     varchar(36),
    input_params    json
);

comment on column public.share_record.share_record_id is '素材分享记录ID';

comment on column public.share_record.created_time is '创建时间';

comment on column public.share_record.updated_time is '更新时间';

comment on column public.share_record.delete_status is '删除状态';

comment on column public.share_record.share_source is '分享来源';

comment on column public.share_record.source_id_list is '来源表ID列表';

comment on column public.share_record.creator is '分享者';

comment on column public.share_record.record_uuid is '分享记录UUID';

alter table public.share_record
    owner to ai_video_producer_pro;

create unique index if not exists ix_share_record_record_uuid
    on public.share_record (record_uuid);

create index if not exists ix_share_record_share_record_id
    on public.share_record (share_record_id);

create table if not exists public.shooting
(
    shooting_id        bigserial
        primary key,
    mix_project_id     bigint       default 0,
    shooting_script_id bigint       default 0,
    shooting_config_id bigint       default 0,
    create_time        timestamp,
    last_update        timestamp,
    index              integer      default 0,
    delete_status      deletestatus default 'NORMAL'::deletestatus,
    name               varchar(255),
    creator            bigint       default 0
);

alter table public.shooting
    owner to ai_video_producer_pro;

create table if not exists public.shooting_config
(
    shooting_config_id      serial
        primary key,
    create_time             timestamp,
    last_update             timestamp,
    delete_status           deletestatus,
    sticker                 json,
    mix_type                shootingmixtype,
    tts                     json,
    retain_original_sound   boolean,
    duration_type           shootingdurationtype,
    captions                json,
    sound_effect            json,
    main_audio_config       json default '{"main_audio": "original", "range": "background_audio"}'::json,
    dubbing_config          json default '{}'::json,
    foreground_video_config json default '{}'::json
);

comment on column public.shooting_config.shooting_config_id is '混剪工程id';

comment on column public.shooting_config.create_time is '创建时间';

comment on column public.shooting_config.last_update is '更新时间';

comment on column public.shooting_config.delete_status is '删除状态';

comment on column public.shooting_config.sticker is '贴纸配置';

comment on column public.shooting_config.mix_type is '参与混剪方式';

comment on column public.shooting_config.tts is 'TTS配音';

comment on column public.shooting_config.retain_original_sound is '是否保留视频原声';

comment on column public.shooting_config.duration_type is '镜头时长类型';

comment on column public.shooting_config.captions is '字幕配置';

comment on column public.shooting_config.main_audio_config is '主音频配置，包括音频类型和音频范围';

comment on column public.shooting_config.dubbing_config is '配音配置';

comment on column public.shooting_config.foreground_video_config is '前景视频配置';

alter table public.shooting_config
    owner to ai_video_producer_pro;

create index if not exists ix_shooting_config_shooting_config_id
    on public.shooting_config (shooting_config_id);

create table if not exists public.shooting_script
(
    shooting_script_id bigserial
        primary key,
    created_time       timestamp,
    updated_time       timestamp,
    delete_status      deletestatus,
    story_board_id     bigint not null,
    sort_order         integer,
    pre_material_info  json,
    name               varchar,
    lines              varchar,
    dubbing_address    varchar,
    description        varchar,
    scene_semantic     text,
    agent_extra_info   json
);

comment on column public.shooting_script.shooting_script_id is '分镜脚本ID';

comment on column public.shooting_script.created_time is '创建时间';

comment on column public.shooting_script.updated_time is '更新时间';

comment on column public.shooting_script.delete_status is '删除状态';

comment on column public.shooting_script.story_board_id is '故事板ID';

comment on column public.shooting_script.sort_order is '排序';

comment on column public.shooting_script.pre_material_info is '预先配置的素材信息';

comment on column public.shooting_script.name is '分镜脚本名称';

comment on column public.shooting_script.lines is '分镜脚本文案';

comment on column public.shooting_script.dubbing_address is '配音地址';

comment on column public.shooting_script.description is '分镜脚本描述';

alter table public.shooting_script
    owner to ai_video_producer_pro;

create index if not exists ix_shooting_script_shooting_script_id
    on public.shooting_script (shooting_script_id);

create index if not exists ix_shooting_script_story_board_id
    on public.shooting_script (story_board_id);

create table if not exists public.sku
(
    sku_uid       varchar(64) not null
        primary key,
    name          varchar(255),
    merchant_uid  varchar(64),
    create_time   timestamp,
    merchant_name varchar(255),
    category      varchar(255),
    sku_order     bigint
);

alter table public.sku
    owner to ai_video_producer_pro;

create table if not exists public.story_board
(
    story_board_id     bigserial
        primary key,
    created_time       timestamp,
    updated_time       timestamp,
    delete_status      deletestatus,
    name               varchar,
    project_id         varchar,
    creator_uid        integer,
    story_board_source enumstoryboardsource,
    source_task_id     integer,
    source_material_id integer,
    abstract           text
);

comment on column public.story_board.story_board_id is '脚本故事板ID';

comment on column public.story_board.created_time is '创建时间';

comment on column public.story_board.updated_time is '更新时间';

comment on column public.story_board.delete_status is '删除状态';

comment on column public.story_board.name is '故事板名称';

comment on column public.story_board.project_id is '项目ID';

comment on column public.story_board.creator_uid is '创建者UID';

comment on column public.story_board.story_board_source is '故事板来源';

comment on column public.story_board.source_task_id is '来源任务ID';

alter table public.story_board
    owner to ai_video_producer_pro;

create index if not exists ix_story_board_story_board_id
    on public.story_board (story_board_id);

create table if not exists public.task_deepspace_material_ana_check
(
    deepspace_task_uid varchar(128),
    original_resp      json,
    sub_task_id        bigint default nextval('sub_task_deepspace_material_ana_check_sub_task_id_seq'::regclass) not null
        constraint sub_task_deepspace_material_ana_check_pkey
            primary key,
    status             varchar(255),
    vlc_id             varchar(255),
    material_uuid      text
);

comment on table public.task_deepspace_material_ana_check is '深空素材解析进度';

comment on column public.task_deepspace_material_ana_check.status is '状态信息， 用于筛选';

comment on column public.task_deepspace_material_ana_check.vlc_id is '视频的id';

alter table public.task_deepspace_material_ana_check
    owner to ai_video_producer_pro;

create index if not exists status_index
    on public.task_deepspace_material_ana_check (status);

create table if not exists public.task_story_board_ana
(
    sub_task_id  bigint default nextval('sub_task_story_board_ana_sub_task_id_seq'::regclass) not null
        constraint sub_task_story_board_ana_pkey
            primary key,
    material_id  bigint,
    err_msg      varchar(2048),
    project_id   varchar(64),
    result       json,
    input_params json,
    video_id     varchar,
    end_time     timestamp
);

comment on table public.task_story_board_ana is '故事板解析任务';

alter table public.task_story_board_ana
    owner to ai_video_producer_pro;

create table if not exists public.task_agent_long_to_short
(
    sub_task_id  serial
        primary key,
    input_params json,
    result       json,
    err_msg      varchar,
    ext_info     json,
    created_time timestamp,
    updated_time timestamp,
    start_time   timestamp,
    end_time     timestamp
);

alter table public.task_agent_long_to_short
    owner to ai_video_producer_pro;

create table if not exists public.task_ai_script
(
    sub_task_id  bigserial
        primary key,
    input_params json,
    ext_info     json,
    result       json,
    err_msg      text,
    create_time  timestamp,
    last_update  timestamp,
    start_time   timestamp,
    end_time     timestamp
);

alter table public.task_ai_script
    owner to ai_video_producer_pro;

create table if not exists public.task_asr
(
    sub_task_id  bigserial
        primary key,
    input_params json,
    result       json,
    err_msg      text,
    ext_info     json,
    create_time  timestamp,
    start_time   timestamp,
    end_time     timestamp,
    last_update  timestamp,
    media_id     varchar not null,
    language     varchar(255),
    output_type  varchar(255)
);

alter table public.task_asr
    owner to ai_video_producer_pro;

create table if not exists public.task_dedup
(
    sub_task_id  bigserial
        constraint task_dedup_pk
            primary key,
    input_params json,
    result       json,
    err_msg      text,
    ext_info     json,
    create_time  timestamp default CURRENT_TIMESTAMP,
    last_update  timestamp default CURRENT_TIMESTAMP,
    start_time   timestamp,
    end_time     timestamp,
    output_video varchar,
    job_id       varchar
);

alter table public.task_dedup
    owner to ai_video_producer_pro;

create table if not exists public.task_dubbing
(
    sub_task_id  bigserial
        primary key,
    input_params json,
    result       json,
    err_msg      text,
    ext_info     json,
    create_time  timestamp(6) default CURRENT_TIMESTAMP,
    last_update  timestamp(6) default CURRENT_TIMESTAMP,
    start_time   timestamp(6),
    end_time     timestamp(6),
    output_audio varchar(255),
    job_id       varchar(64),
    params_hash  varchar(255)
);

alter table public.task_dubbing
    owner to ai_video_producer_pro;

create table if not exists public.task_erase
(
    sub_task_id  bigserial
        primary key,
    input_params json,
    result       json,
    err_msg      text,
    ext_info     json,
    create_time  timestamp(6) default CURRENT_TIMESTAMP,
    last_update  timestamp(6) default CURRENT_TIMESTAMP,
    start_time   timestamp(6),
    end_time     timestamp(6),
    output_video varchar(255),
    job_id       varchar(64)
);

alter table public.task_erase
    owner to ai_video_producer_pro;

create table if not exists public.task_face_swap
(
    sub_task_id     bigint default nextval('task_asr_sub_task_id_seq'::regclass) not null
        constraint task_asr_copy1_pkey
            primary key,
    input_params    json,
    result          json,
    err_msg         text,
    ext_info        json,
    create_time     timestamp(6),
    start_time      timestamp(6),
    end_time        timestamp(6),
    last_update     timestamp(6),
    source_image    varchar(255),
    target_video    varchar(255),
    output_video    varchar(255),
    target_video_st double precision,
    target_video_et double precision
);

comment on column public.task_face_swap.source_image is '需要替换的脸的图片';

comment on column public.task_face_swap.target_video is '需要换脸的视频';

comment on column public.task_face_swap.output_video is '输出的视频';

alter table public.task_face_swap
    owner to ai_video_producer_pro;

create table if not exists public.task_gen_caption
(
    sub_task_id    bigserial
        primary key,
    mix_project_id bigint,
    shooting_id    bigint,
    input_params   json,
    result         json,
    err_msg        text,
    params_hash    varchar(255)
);

alter table public.task_gen_caption
    owner to ai_video_producer_pro;

create table if not exists public.task_gen_tts
(
    sub_task_id    bigserial
        primary key,
    mix_project_id bigint,
    shooting_id    bigint,
    input_params   json,
    err_msg        text,
    result         json,
    params_hash    varchar(255)
);

comment on column public.task_gen_tts.sub_task_id is '子任务ID';

comment on column public.task_gen_tts.mix_project_id is '混合项目ID';

comment on column public.task_gen_tts.shooting_id is '拍摄ID';

comment on column public.task_gen_tts.input_params is '输入参数';

comment on column public.task_gen_tts.err_msg is '错误信息';

comment on column public.task_gen_tts.result is '执行结果';

comment on column public.task_gen_tts.params_hash is '参数哈希值';

alter table public.task_gen_tts
    owner to ai_video_producer_pro;

create table if not exists public.task_general
(
    sub_task_id  bigserial
        primary key,
    input_params json not null,
    result       json,
    ext_info     json,
    err_msg      text,
    create_time  timestamp,
    last_update  timestamp,
    start_time   timestamp,
    end_time     timestamp,
    task_type    varchar(255)
);

alter table public.task_general
    owner to ai_video_producer_pro;

create table if not exists public.task_img2video
(
    sub_task_id     bigserial
        primary key,
    input_params    json,
    result          json,
    err_msg         text,
    ext_info        json,
    create_time     timestamp(6),
    end_time        timestamp(6),
    last_update     timestamp(6),
    model           varchar(255) not null,
    image           varchar(1024),
    image_tail      varchar(1024),
    prompt          text,
    negative_prompt text,
    duration        double precision,
    cfg_scale       double precision,
    res_vlc_id      varchar(1024),
    material_ids    bigint[],
    gen_task_id     varchar(256)
);

alter table public.task_img2video
    owner to ai_video_producer_pro;

create table if not exists public.task_lip_sync
(
    sub_task_id  bigint default nextval('task_asr_sub_task_id_seq'::regclass) not null
        constraint task_face_swap_copy1_pkey
            primary key,
    input_params json,
    result       json,
    err_msg      text,
    ext_info     json,
    create_time  timestamp(6),
    start_time   timestamp(6),
    end_time     timestamp(6),
    last_update  timestamp(6),
    source_audio varchar(255)                                                 not null,
    target_video varchar(255)                                                 not null,
    output_video varchar(255)
);

comment on column public.task_lip_sync.source_audio is '需要替换的脸的图片';

comment on column public.task_lip_sync.target_video is '需要换脸的视频';

comment on column public.task_lip_sync.output_video is '输出的视频';

alter table public.task_lip_sync
    owner to ai_video_producer_pro;

create table if not exists public.task_mix_artwork
(
    sub_task_id  bigserial
        primary key,
    input_params json,
    result       json,
    err_msg      text,
    ext_info     json,
    create_time  timestamp,
    start_time   timestamp,
    end_time     timestamp,
    last_update  timestamp,
    output_video varchar(255)
);

comment on column public.task_mix_artwork.output_video is '输出视频的id';

alter table public.task_mix_artwork
    owner to ai_video_producer_pro;

create table if not exists public.task_mj_txt2img
(
    sub_task_id   bigserial
        primary key,
    input_params  json,
    result        json,
    err_msg       text,
    ext_info      json,
    create_time   timestamp(6),
    start_time    timestamp(6),
    end_time      timestamp(6),
    last_update   timestamp(6),
    model         varchar(255),
    action        varchar(128),
    run_mode      varchar(128),
    prompt        text,
    image_task_id bigint,
    image_index   integer,
    gen_task_id   bigint,
    res_vlc_id    varchar(1024),
    material_ids  bigint[]
);

alter table public.task_mj_txt2img
    owner to ai_video_producer_pro;

create table if not exists public.task_seg
(
    sub_task_id  bigserial
        primary key,
    input_params json,
    result       json,
    err_msg      text,
    ext_info     json,
    create_time  timestamp(6) default CURRENT_TIMESTAMP,
    last_update  timestamp(6) default CURRENT_TIMESTAMP,
    start_time   timestamp(6),
    end_time     timestamp(6),
    output_video varchar(255),
    job_id       varchar(64)
);

alter table public.task_seg
    owner to ai_video_producer_pro;

create table if not exists public.task_uvr
(
    sub_task_id   bigserial
        primary key,
    input_params  json,
    result        json,
    err_msg       text,
    ext_info      json,
    create_time   timestamp,
    end_time      timestamp,
    last_update   timestamp,
    media_id      varchar not null,
    back_audio_id varchar(255),
    fore_audio_id varchar(255)
);

alter table public.task_uvr
    owner to ai_video_producer_pro;

create table if not exists public.tts_result
(
    tts_id           bigserial
        primary key,
    text             varchar(5120),
    voice_id         varchar(128),
    result_store_uri varchar(1024),
    audio_len        real,
    audio_file_size  bigint,
    gen_start_time   timestamp,
    gen_end_time     timestamp,
    config           json,
    vlc_id           varchar(64)
);

alter table public.tts_result
    owner to ai_video_producer_pro;

create table if not exists public.tts_voices
(
    voice_id        varchar(128) not null
        primary key,
    engine          varchar(64),
    target_voice_id varchar(128),
    label           varchar(128),
    delete_status   deletestatus default 'NORMAL'::deletestatus,
    gender          varchar,
    accent          varchar,
    scene           varchar,
    emotion         varchar,
    url             varchar,
    tags_name       character varying[],
    model_id        varchar,
    language_code   varchar
);

comment on column public.tts_voices.gender is '性别';

comment on column public.tts_voices.accent is '口音';

comment on column public.tts_voices.scene is '场景';

comment on column public.tts_voices.emotion is '情绪';

comment on column public.tts_voices.url is '试听url';

alter table public.tts_voices
    owner to ai_video_producer_pro;

create table if not exists public.upload_record
(
    upload_record_id bigint  default nextval('upload_record_record_id_seq'::regclass) not null
        primary key,
    created_time     timestamp,
    updated_time     timestamp,
    delete_status    deletestatus,
    upload_source    varchar,
    record_uuid      varchar(36),
    upload_status    enumuploadstatus,
    project_id       varchar,
    filename         varchar,
    result           json,
    extra_args       json,
    md5              varchar(255),
    is_read          boolean default false
);

comment on column public.upload_record.upload_record_id is '上传记录ID';

comment on column public.upload_record.created_time is '创建时间';

comment on column public.upload_record.updated_time is '更新时间';

comment on column public.upload_record.delete_status is '删除状态';

comment on column public.upload_record.upload_source is '上传状态';

comment on column public.upload_record.record_uuid is '上传记录UUID';

comment on column public.upload_record.upload_status is '上传状态';

comment on column public.upload_record.project_id is '项目ID';

comment on column public.upload_record.filename is '文件名';

comment on column public.upload_record.result is '上传结果';

comment on column public.upload_record.extra_args is '素材来源所需额外参数';

comment on column public.upload_record.is_read is '是否已读';

alter table public.upload_record
    owner to ai_video_producer_pro;

create index if not exists ix_upload_record_record_id
    on public.upload_record (upload_record_id);

create unique index if not exists ix_upload_record_record_uuid
    on public.upload_record (record_uuid);

create table if not exists public.user_white_list_record
(
    user_white_record_id bigserial
        primary key,
    created_time         timestamp,
    updated_time         timestamp,
    delete_status        deletestatus,
    name                 varchar
);

comment on column public.user_white_list_record.user_white_record_id is '换脸解析ID';

comment on column public.user_white_list_record.created_time is '创建时间';

comment on column public.user_white_list_record.updated_time is '更新时间';

comment on column public.user_white_list_record.delete_status is '删除状态';

comment on column public.user_white_list_record.name is '用户名称';

alter table public.user_white_list_record
    owner to ai_video_producer_pro;

create unique index if not exists idx_user_white_list_record_name
    on public.user_white_list_record (name);

create table if not exists public.video_clip_record
(
    video_clip_record_id bigserial
        primary key,
    created_time         timestamp,
    updated_time         timestamp,
    delete_status        deletestatus,
    material_id          bigint,
    video_clip_source    varchar,
    shooting_id          bigint,
    shooting_script_id   bigint,
    second_start         double precision,
    second_end           double precision,
    asr_content          varchar,
    cdn_url              varchar,
    post_cdn_url         varchar,
    material_name        varchar,
    sort_order           integer default 1
);

comment on column public.video_clip_record.video_clip_record_id is '视频片段记录ID';

comment on column public.video_clip_record.created_time is '创建时间';

comment on column public.video_clip_record.updated_time is '更新时间';

comment on column public.video_clip_record.delete_status is '删除状态';

comment on column public.video_clip_record.material_id is '素材ID';

comment on column public.video_clip_record.video_clip_source is '视频片段来源';

comment on column public.video_clip_record.shooting_id is '分镜ID';

comment on column public.video_clip_record.shooting_script_id is '分镜脚本ID';

comment on column public.video_clip_record.second_start is '视频片段开始时间';

comment on column public.video_clip_record.second_end is '视频片段结束时间';

comment on column public.video_clip_record.asr_content is 'ASR内容';

comment on column public.video_clip_record.cdn_url is '切片CDN地址';

comment on column public.video_clip_record.post_cdn_url is '切片封面图CDN地址';

comment on column public.video_clip_record.material_name is '素材名称';

alter table public.video_clip_record
    owner to ai_video_producer_pro;

create index if not exists ix_video_clip_record_shooting_id
    on public.video_clip_record (shooting_id);

create index if not exists ix_video_clip_record_shooting_script_id
    on public.video_clip_record (shooting_script_id);

create index if not exists ix_video_clip_record_video_clip_record_id
    on public.video_clip_record (video_clip_record_id);

create table if not exists public.vlc_callback_record
(
    vlc_callback_record_id bigserial
        primary key,
    created_time           timestamp,
    updated_time           timestamp,
    delete_status          deletestatus,
    request_id             varchar,
    version                varchar,
    event_type             varchar,
    event_time             varchar,
    data                   json
);

comment on column public.vlc_callback_record.vlc_callback_record_id is 'VLC回调记录ID';

comment on column public.vlc_callback_record.created_time is '创建时间';

comment on column public.vlc_callback_record.updated_time is '更新时间';

comment on column public.vlc_callback_record.delete_status is '删除状态';

comment on column public.vlc_callback_record.request_id is '请求的唯一 ID';

comment on column public.vlc_callback_record.version is '回调版本';

comment on column public.vlc_callback_record.event_type is '事件名称';

comment on column public.vlc_callback_record.event_time is '事件产生时间，UTC 时间，精度为秒。';

comment on column public.vlc_callback_record.data is '回调内容，JSON 格式。';

alter table public.vlc_callback_record
    owner to ai_video_producer_pro;

create table if not exists public.func_list
(
    func_id     serial
        primary key,
    func_type   varchar(50),
    description varchar(50),
    tags        varchar(50),
    icon        varchar,
    name        varchar
);

comment on column public.func_list.func_type is '功能唯一类型';

comment on column public.func_list.description is '功能描述';

comment on column public.func_list.icon is 'icon';

comment on column public.func_list.name is '功能名称';

alter table public.func_list
    owner to ai_video_producer_pro;

create table if not exists public.func_common
(
    sub_func_id serial
        primary key,
    user_uid    integer,
    func_type   varchar(50),
    tags        varchar(50),
    status      boolean
);

comment on column public.func_common.status is '添加状态';

alter table public.func_common
    owner to ai_video_producer_pro;

create table if not exists public.task_batch_download_artwork
(
    sub_task_id     serial
        primary key,
    input_params    json,
    result          json,
    err_msg         text,
    ext_info        json,
    create_time     timestamp,
    last_update     timestamp,
    start_time      timestamp,
    end_time        timestamp,
    params_hash     varchar(128),
    zip_file_vlc_id varchar(64),
    zip_file_name   varchar(255),
    is_downloaded   boolean default true
);

alter table public.task_batch_download_artwork
    owner to ai_video_producer_pro;

create table if not exists public.task_agent_voice_mash
(
    sub_task_id  serial
        primary key,
    input_params json,
    result       json,
    err_msg      varchar,
    ext_info     json,
    created_time timestamp,
    updated_time timestamp,
    start_time   timestamp,
    end_time     timestamp
);

alter table public.task_agent_voice_mash
    owner to ai_video_producer_pro;

create table if not exists public.log_entry
(
    log_entry_id  serial
        primary key,
    created_time  timestamp,
    updated_time  timestamp,
    task_id       integer,
    sub_task_id   integer,
    function_name varchar,
    input_params  json,
    result        json,
    err_msg       varchar,
    ext_info      json
);

alter table public.log_entry
    owner to ai_video_producer_pro;

create table if not exists public.base_task_ts
(
    task_id        bigserial
        primary key,
    task_type      varchar(255) not null,
    data_source    varchar(255) not null,
    source_id_list json,
    status         varchar(255) not null
);

alter table public.base_task_ts
    owner to ai_video_producer_pro;

create table if not exists public.user_team_setting
(
    record_id       bigserial
        constraint record_pk
            primary key,
    user_uid        bigint,
    last_team_id    varchar(64),
    last_space_type space_type default 'TEAM'::space_type not null,
    last_update     timestamp
);

comment on table public.user_team_setting is '用户团队设置';

comment on column public.user_team_setting.record_id is '主键ID';

comment on column public.user_team_setting.user_uid is '用户UID';

comment on column public.user_team_setting.last_team_id is '用户最后设置的团队ID';

comment on column public.user_team_setting.last_space_type is '最后一次选择的空间类型';

comment on column public.user_team_setting.last_update is '最后更新时间';

alter table public.user_team_setting
    owner to ai_video_producer_pro;

create table if not exists public.task_agent_voice_mash_v1
(
    sub_task_id  serial
        primary key,
    input_params json,
    result       json,
    err_msg      varchar,
    ext_info     json,
    created_time timestamp,
    updated_time timestamp,
    start_time   timestamp,
    end_time     timestamp
);

alter table public.task_agent_voice_mash_v1
    owner to ai_video_producer_pro;

create table if not exists public.baidu_video_abstract_vector
(
    vector_id     bigserial
        primary key,
    material_id   bigint,
    abstract      text,
    vector        vector,
    create_time   timestamp(6) default CURRENT_TIMESTAMP,
    last_update   timestamp(6) default CURRENT_TIMESTAMP,
    start_time    integer,
    end_time      integer,
    content_id    varchar(255),
    level         integer,
    sub_task_id   bigint,
    material_uuid uuid,
    vlc_id        varchar(255)
);

comment on column public.baidu_video_abstract_vector.start_time is 'clip 开始时间';

comment on column public.baidu_video_abstract_vector.end_time is 'clip 结束时间';

alter table public.baidu_video_abstract_vector
    owner to ai_video_producer_pro;

create table if not exists public.task_baidu_video_abstract
(
    sub_task_id  bigserial
        primary key,
    input_params json,
    result       json,
    material_id  bigint,
    err_msg      text,
    ext_info     json,
    create_time  timestamp(6) default CURRENT_TIMESTAMP,
    last_update  timestamp(6) default CURRENT_TIMESTAMP,
    start_time   timestamp(6),
    end_time     timestamp(6),
    vlc_id       varchar(255)
);

alter table public.task_baidu_video_abstract
    owner to ai_video_producer_pro;

create table if not exists public.material_v1
(
    material_id            bigserial
        primary key,
    created_time           timestamp,
    updated_time           timestamp,
    delete_status          deletestatus,
    material_name          varchar,
    material_source        enummaterialsource,
    material_type          varchar,
    creator_uid            integer,
    vlc_id                 varchar,
    source_info            json,
    project_id             varchar,
    mix_project_id         integer,
    upload_record_id       bigint,
    deepspace_index_status enumdeepspaceindexstatus,
    asr_deepspace          json,
    asr_blueai             json,
    parent_id              bigint,
    tag_list               character varying[],
    material_uuid          uuid default uuid_generate_v4() not null
        constraint material_uuid
            unique,
    root_id                integer,
    scene_deepspace        json,
    plot_deepspace         json,
    agent_deepspace        json,
    baidu_index_status     enumdeepspaceindexstatus
);

alter table public.material_v1
    owner to ai_video_producer_pro;

