
SELECT
    n.nspname AS schema_name,
    t.typname AS type_name,
    pg_catalog.format_type(t.oid, NULL) AS type_identifier,
    ARRAY(
        SELECT e.enumlabel
        FROM pg_catalog.pg_enum e
        WHERE e.enumtypid = t.oid
        ORDER BY e.enumsortorder
    ) AS enum_values,
    CASE
        WHEN t.typrelid > 0 THEN 'Composite type (table or composite type)'
        WHEN t.typtype = 'd' THEN 'Domain'
        WHEN t.typtype = 'e' THEN 'Enumeration'
        WHEN t.typtype = 'r' THEN 'Range'
        WHEN t.typtype = 'c' THEN 'Composite'
        ELSE 'Other type'
    END AS type_kind,
    pg_catalog.obj_description(t.oid, 'pg_type') AS type_description
FROM
    pg_catalog.pg_type t
LEFT JOIN
    pg_catalog.pg_namespace n ON n.oid = t.typnamespace
WHERE
    (t.typrelid = 0 OR (SELECT c.relkind = 'c' FROM pg_catalog.pg_class c WHERE c.oid = t.typrelid))
    AND NOT EXISTS(SELECT 1 FROM pg_catalog.pg_type el WHERE el.oid = t.typelem AND el.typarray = t.oid)
    AND n.nspname NOT IN ('pg_catalog', 'information_schema')
ORDER BY
    schema_name,
    type_name;

---
public,deletestatus,deletestatus,"{NORMAL,DELETED}",Enumeration,
public,enumdeepspaceindexstatus,enumdeepspaceindexstatus,"{NEW,SUCCESS,PROCESSING,FAIL}",Enumeration,
public,enummaterialsource,enummaterialsource,"{user_upload,ai_slice,mix_artwork}",Enumeration,
public,enummaterialtype,enummaterialtype,"{video,image,audio}",Enumeration,
public,enumstoryboardsource,enumstoryboardsource,"{user,ai}",Enumeration,
public,enumuploadsource,enumuploadsource,"{material_library,storyboard_script,video_to_script}",Enumeration,
public,enumuploadstatus,enumuploadstatus,"{not_uploaded,uploading,uploaded}",Enumeration,
public,event_type,event_type,"{BUSINESS,DATABASE,SYSTEM,ERROR,OTHER}",Enumeration,
public,operation_type,operation_type,"{ADD,DELETE,UPDATE,QUERY,OTHER}",Enumeration,
public,shooting_duration_type,shooting_duration_type,"{SLICE_ORIGINAL,FOLLOW_TTS}",Enumeration,镜头混剪时长类型
public,shooting_mix_type,shooting_mix_type,"{RANDOM_ONE,ALL}",Enumeration,
public,shootingdurationtype,shootingdurationtype,"{SLICE_ORIGINAL,FOLLOW_TTS}",Enumeration,
public,shootingmixtype,shootingmixtype,"{RANDOM_ONE,ALL}",Enumeration,
public,space_type,space_type,"{PRIVATE,TEAM,OTHER}",Enumeration,
public,task_status,task_status,"{PENDING,PROCESSING,SUCCESS,FAIL}",Enumeration,
public,task_type,task_type,"{TASK_STORY_BOARD_ANA,TASK_MIX_ARTWORK_EXPORT,TASK_MUSE_MATERIAL_PUSH,TASK_FINE_ARTWORK_PULL,TASK_FACE_SWAP,TASK_LIP_SYNC}",Enumeration,任务类型
public,vector,vector,{},Other type,
