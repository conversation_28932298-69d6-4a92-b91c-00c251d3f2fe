WITH task_counts AS (
                SELECT
                    DATE(create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) AS total_success_tasks,
                    COUNT(CASE WHEN status = 'FAIL' THEN 1 END) AS total_fail_tasks,
                    COUNT(CASE WHEN status = 'PROCESSING' THEN 1 END) AS total_processing_tasks,
                    COUNT(CASE WHEN status = 'PENDING' THEN 1 END) AS total_pending_tasks
                FROM base_task
                WHERE (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            new_materials AS (
                SELECT
                    DATE(created_time AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS new_materials
                FROM material
                WHERE (created_time AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (created_time AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            new_scripts AS (
                SELECT
                    DATE(created_time AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS new_scripts
                FROM story_board
                WHERE (created_time AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (created_time AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            new_mix_projects AS (
                SELECT
                    DATE(create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS new_mix_projects
                FROM mix_project
                WHERE (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            new_agent_projects AS (
                SELECT
                    DATE(created_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS new_agent_projects
                FROM agent_project
                WHERE (created_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (created_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            active_users AS (
                SELECT 
                    DATE(m.created_time AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(DISTINCT m.creator_uid) AS active_users
                FROM material m
                WHERE (m.created_time AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (m.created_time AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            ai_text_to_image AS (
                SELECT
                    DATE(create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS ai_text_to_image
                FROM task_mj_txt2img
                WHERE (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            ai_image_to_video AS (
                SELECT
                    DATE(create_time AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(CASE WHEN model LIKE '%kling%' THEN 1 END) AS ai_image_to_video_kling,
                    COUNT(CASE WHEN model NOT LIKE '%kling%' THEN 1 END) AS ai_image_to_video_minimax
                FROM task_img2video
                WHERE (create_time AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (create_time AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            ai_caption_erase AS (
                SELECT
                    DATE(create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS ai_caption_erase
                FROM task_erase
                WHERE (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            ai_face_swap AS (
                SELECT
                    DATE(create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS ai_face_swap
                FROM task_face_swap
                WHERE (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (create_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            ),
            ai_video_analysis AS (
                SELECT
                    DATE(end_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') AS date,
                    COUNT(*) AS ai_video_analysis
                FROM task_story_board_ana
                WHERE (end_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') >= :start_time
                  AND (end_time AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Shanghai') < :end_time
                GROUP BY date
            )
            SELECT
                COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date, au.date, ati.date, aiv.date, ace.date, afs.date, ava.date) AS date,
                COALESCE(tc.total_success_tasks, 0) AS total_success_tasks,
                COALESCE(tc.total_fail_tasks, 0) AS total_fail_tasks,
                COALESCE(tc.total_processing_tasks, 0) AS total_processing_tasks,
                COALESCE(tc.total_pending_tasks, 0) AS total_pending_tasks,
                COALESCE(nm.new_materials, 0) AS new_materials,
                COALESCE(ns.new_scripts, 0) AS new_scripts,
                COALESCE(nmp.new_mix_projects, 0) AS new_mix_projects,
                COALESCE(nap.new_agent_projects, 0) AS new_agent_projects,
                COALESCE(au.active_users, 0) AS active_users,
                COALESCE(ati.ai_text_to_image, 0) AS ai_text_to_image,
                COALESCE(aiv.ai_image_to_video_kling, 0) AS ai_image_to_video_kling,
                COALESCE(aiv.ai_image_to_video_minimax, 0) AS ai_image_to_video_minimax,
                COALESCE(ace.ai_caption_erase, 0) AS ai_caption_erase,
                COALESCE(afs.ai_face_swap, 0) AS ai_face_swap,
                COALESCE(ava.ai_video_analysis, 0) AS ai_video_analysis
            FROM task_counts tc
            FULL OUTER JOIN new_materials nm ON tc.date = nm.date
            FULL OUTER JOIN new_scripts ns ON COALESCE(tc.date, nm.date) = ns.date
            FULL OUTER JOIN new_mix_projects nmp ON COALESCE(tc.date, nm.date, ns.date) = nmp.date
            FULL OUTER JOIN new_agent_projects nap ON COALESCE(tc.date, nm.date, ns.date, nmp.date) = nap.date
            FULL OUTER JOIN active_users au ON COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date) = au.date
            FULL OUTER JOIN ai_text_to_image ati ON COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date, au.date) = ati.date
            FULL OUTER JOIN ai_image_to_video aiv ON COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date, au.date, ati.date) = aiv.date
            FULL OUTER JOIN ai_caption_erase ace ON COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date, au.date, ati.date, aiv.date) = ace.date
            FULL OUTER JOIN ai_face_swap afs ON COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date, au.date, ati.date, aiv.date, ace.date) = afs.date
            FULL OUTER JOIN ai_video_analysis ava ON COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date, au.date, ati.date, aiv.date, ace.date, afs.date) = ava.date
            WHERE COALESCE(tc.date, nm.date, ns.date, nmp.date, nap.date, au.date, ati.date, aiv.date, ace.date, afs.date, ava.date) IS NOT NULL
            ORDER BY date