import requests
import json
import time
from typing import List, Dict, Any, Optional

class FeishuTableConnector:
    def __init__(self, app_id: str, app_secret: str, table_id: str, view_id: str):
        """
        初始化飞书互动表格连接器
        
        Args:
            app_id: 飞书应用的App ID
            app_secret: 飞书应用的App Secret
            table_id: 表格的ID
            view_id: 视图的ID
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.table_id = table_id
        self.view_id = view_id
        self.access_token = None
        self.token_expire_time = 0
        self.base_url = "https://open.feishu.cn/open-apis/bitable/v1"
        
    def _get_access_token(self) -> str:
        """获取飞书访问令牌"""
        if self.access_token and self.token_expire_time > time.time() + 60:
            return self.access_token
            
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        response = requests.post(url, json=payload)
        response.raise_for_status()
        data = response.json()
        
        if data.get("code") != 0:
            raise Exception(f"获取访问令牌失败: {data.get('msg')}")
            
        self.access_token = data.get("tenant_access_token")
        self.token_expire_time = time.time() + data.get("expire", 0)
        return self.access_token
        
    def _make_request(self, method: str, endpoint: str, payload: Optional[Dict] = None) -> Dict:
        """发送API请求"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {self._get_access_token()}",
            "Content-Type": "application/json"
        }
        
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                if method == "GET":
                    response = requests.get(url, headers=headers)
                elif method == "POST":
                    response = requests.post(url, headers=headers, json=payload)
                elif method == "PUT":
                    response = requests.put(url, headers=headers, json=payload)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                    
                response.raise_for_status()
                return response.json()
            except requests.exceptions.RequestException as e:
                retry_count += 1
                if retry_count >= max_retries:
                    raise Exception(f"API请求失败: {str(e)}")
                time.sleep(2)  # 重试前等待2秒
                
    def get_records(self, fields: Optional[List[str]] = None, filter_formula: Optional[str] = None) -> List[Dict]:
        """
        获取表格记录
        
        Args:
            fields: 需要返回的字段列表
            filter_formula: 过滤条件公式
            
        Returns:
            记录列表
        """
        endpoint = f"/app_tables/{self.table_id}/records"
        params = {
            "view_id": self.view_id,
            "page_size": 100
        }
        
        if fields:
            params["fields"] = json.dumps(fields)
            
        if filter_formula:
            params["filter"] = filter_formula
            
        all_records = []
        page_token = None
        
        while True:
            if page_token:
                params["page_token"] = page_token
                
            response = self._make_request("GET", endpoint, params)
            data = response.get("data", {})
            all_records.extend(data.get("items", []))
            
            page_token = data.get("page_token")
            if not page_token or not data.get("has_more"):
                break
                
        return all_records
        
    def batch_add_records(self, records: List[Dict]) -> Dict:
        """
        批量添加记录
        
        Args:
            records: 记录列表，每个记录是一个字段字典
            
        Returns:
            添加结果
        """
        endpoint = f"/app_tables/{self.table_id}/records/batch_create"
        payload = {
            "records": [{"fields": record} for record in records]
        }
        return self._make_request("POST", endpoint, payload)
        
    def batch_update_records(self, records: List[Dict]) -> Dict:
        """
        批量更新记录
        
        Args:
            records: 记录列表，每个记录包含record_id和fields
            
        Returns:
            更新结果
        """
        endpoint = f"/app_tables/{self.table_id}/records/batch_update"
        payload = {
            "records": records
        }
        return self._make_request("POST", endpoint, payload)
        
    def deduplicate_and_insert(self, new_records: List[Dict], unique_fields: List[str]) -> Dict:
        """
        去重并插入新记录
        
        Args:
            new_records: 新记录列表
            unique_fields: 用于去重的字段列表
            
        Returns:
            处理结果
        """
        # 1. 构建新记录的唯一键集合
        new_unique_keys = set()
        new_records_to_insert = []
        
        for record in new_records:
            unique_key = tuple(record.get(field) for field in unique_fields)
            if unique_key not in new_unique_keys:
                new_unique_keys.add(unique_key)
                new_records_to_insert.append(record)
                
        # 2. 获取已有记录并检查重复
        existing_records = self.get_records(fields=unique_fields)
        existing_unique_keys = set()
        duplicate_records = []
        
        for record in existing_records:
            fields = record.get("fields", {})
            unique_key = tuple(fields.get(field) for field in unique_fields)
            existing_unique_keys.add(unique_key)
            
        final_records_to_insert = []
        
        for record in new_records_to_insert:
            unique_key = tuple(record.get(field) for field in unique_fields)
            if unique_key in existing_unique_keys:
                duplicate_records.append(record)
            else:
                final_records_to_insert.append(record)
                
        # 3. 插入不重复的记录
        if final_records_to_insert:
            result = self.batch_add_records(final_records_to_insert)
        else:
            result = {"data": {"success": 0, "failed": 0}}
            
        return {
            "inserted": len(final_records_to_insert),
            "duplicated": len(duplicate_records),
            "result": result,
            "duplicate_records": duplicate_records
        }

# 使用示例
if __name__ == "__main__":
    # 配置信息 - 请替换为实际值
    APP_ID = "your_app_id"
    APP_SECRET = "your_app_secret"
    TABLE_ID = "your_table_id"
    VIEW_ID = "your_view_id"
    
    # 初始化连接器
    connector = FeishuTableConnector(APP_ID, APP_SECRET, TABLE_ID, VIEW_ID)
    
    # 示例数据 - 替换为实际数据
    new_data = [
        {"姓名": "张三", "电话": "13800138001", "邮箱": "<EMAIL>"},
        {"姓名": "李四", "电话": "13900139001", "邮箱": "<EMAIL>"},
        {"姓名": "王五", "电话": "13800138001", "邮箱": "<EMAIL>"}  # 电话重复
    ]
    
    # 执行去重并插入操作，以电话作为唯一标识
    result = connector.deduplicate_and_insert(new_data, ["电话"])
    
    print(f"成功插入: {result['inserted']} 条记录")
    print(f"发现重复: {result['duplicated']} 条记录")
    print(f"重复记录: {result['duplicate_records']}")    