# Git 相关
.git
.gitignore
.gitattributes

# Python 相关
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.pytest_cache

# 虚拟环境
venv/
ENV/
env/
.venv/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定
logs/*
exports/*
*.xlsx
*.log
*.pid

# Docker 相关
# Dockerfile*  <- Dockerfile本身不应被忽略
# .dockerignore <- .dockerignore本身不应被忽略
docker-compose*

# 文档和说明
README*.md
*.md
docs/

# 测试相关
tests/
test_*

# 部署脚本
deploy.sh
*.sh

# 环境配置
.env*
!.env.example

# 临时文件
tmp/
temp/
*.tmp

# 备份文件
*.bak
*.backup