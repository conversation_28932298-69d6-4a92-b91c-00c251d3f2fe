# 使用官方 Python 3.12 镜像（Debian Bookworm）作为基础镜像
FROM python:3.12-slim

# 设置环境变量，确保 Python 输出立即打印到终端
ENV PYTHONUNBUFFERED=1
# 设置 PIP 不写入 pyc 文件
ENV PYTHONDONTWRITEBYTECODE=1
# 设置 PATH 包含用户安装的包
ENV PATH="/home/<USER>/.local/bin:$PATH"

# 安装系统依赖
# 下面安装系统依赖 (gcc, libpq-dev) 的步骤可能不是必需的。
# 因为你的 requirements.txt 中使用了 psycopg2-binary，它通常自带预编译内容，
# 不需要 gcc 或 libpq-dev 来进行编译。
# 如果确认项目中没有其他 Python 包需要从源码编译 C 扩展，
# 建议删除或注释掉以下 RUN 指令，以减小镜像体积并加快构建速度。
# RUN apt-get update && apt-get install -y \\
#     --no-install-recommends \\
#     gcc \\
#     libpq-dev \\
#     && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 创建一个非 root 用户并在该用户下运行应用
RUN useradd --create-home --shell /bin/bash appuser

# 创建必要的目录并设置权限
RUN mkdir -p /app/logs /app/exports /app/conf && \
    chown -R appuser:appuser /app

# 切换到非 root 用户
USER appuser

# 仅复制依赖文件
COPY --chown=appuser:appuser requirements.txt .

# 安装依赖
# 使用 --user 标志将包安装到用户目录下，避免权限问题
RUN pip install --no-cache-dir --user -r requirements.txt

# 将应用代码复制到容器中
COPY --chown=appuser:appuser . .

# 暴露端口（如果应用有 web 服务）
# EXPOSE 8000

# 健康检查
# 你当前使用的健康检查是导入 DB_CONFIG。这是一个基础检查。
# 如果需要更深入的检查，可以考虑实际连接数据库，示例如下（确保相关库和配置已就绪）：
# HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \\
#  CMD python -c "import sys, os; sys.path.append(os.getcwd()); from sqlalchemy import text; from src.core.database import get_db; db = next(get_db()); db.execute(text('SELECT 1')); print('DB connection OK for health check')" || exit 1
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; import os; sys.path.append('/app'); from src.core.config import DB_CONFIG; print('Health check passed')" || exit 1

CMD ["python", "app.py"]