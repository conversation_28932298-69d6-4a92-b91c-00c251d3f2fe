# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.coverage
htmlcov/
.pytest_cache/
.ruff_cache/
pytest.xml
coverage.xml

# 虚拟环境
venv/
env/
ENV/
.python-version

# IDE/编辑器
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# 项目特定
.env
.env.*
!.env.example
*.log
logs/*
!logs/.gitkeep
exports/*
!exports/.gitkeep
conf/local_settings.py

# 数据库
*.db
*.sqlite
*.sqlite3
postgres-data/

# 临时文件
.tmp/
tmp/
temp/
*.bak
*.tmp

# 飞书应用相关
feishu_credentials.json

# 包管理
poetry.lock

# 系统相关
.DS_Store
Thumbs.db
.directory
.project_define.cursor 